module.exports = {
  env: {
    browser: true,
    es2021: true,
    jest: true,
    'react-native/react-native': true,
    'jest/globals': true,
  },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:prettier/recommended',
    'plugin:react-hooks/recommended',
    'plugin:@typescript-eslint/eslint-recommended',
    'airbnb-base',
    'plugin:jest/style',
    '@react-native',
  ],
  overrides: [
    {
      env: {
        node: true,
      },
      files: ['.eslintrc.{js,cjs}'],
      parserOptions: {
        sourceType: 'script',
      },
    },
    {
      files: ['test/**'],
      plugins: ['jest'],
      extends: ['plugin:jest/recommended'],
    },
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: [
    'react',
    'react-hooks',
    '@typescript-eslint',
    'prettier',
    'react-native',
    'unicorn',
    'jest',
  ],
  rules: {
    'no-console': ['error', { allow: ['warn', 'error'] }],
    // 'max-params': ['error', 3],
    'global-require': 0,
    'import/extensions': 0,
    'import/no-extraneous-dependencies': 0,
    'no-nested-ternary': 0,
    'object-curly-newline': 0,
    'react/react-in-jsx-scope': 'off',
    'arrow-parens': ['error', 'as-needed'],
    camelcase: 2,
    'spaced-comment': 'error',
    quotes: ['error', 'single', { avoidEscape: true }],
    'import/no-duplicates': [
      'error',
      {
        considerQueryString: true,
      },
    ],
    'react-native/no-unused-styles': 2,
    'react-native/split-platform-components': 2,
    'react-native/no-color-literals': 2,
    'react-native/no-raw-text': 1,
    'react-native/no-single-element-style-arrays': 2,
    'max-len': [
      'warn',
      {
        code: 256,
        ignoreStrings: true,
      },
    ],
    'no-empty-function': 'off',
    'react/display-name': 'off',
    'react/prop-types': 'off',
    'class-methods-use-this': 0,
    'no-param-reassign': 0,
    'prefer-destructuring': 2,
    'no-use-before-define': [
      'error',
      {
        functions: true,
        classes: true,
        variables: false,
      },
    ],
    'unicorn/consistent-destructuring': 'error',
    'unicorn/no-nested-ternary': 'off',
    'unicorn/prefer-ternary': ['error', 'always'],
    'unicorn/prefer-type-error': 'error',
    'unicorn/require-array-join-separator': 'error',
    'unicorn/prefer-switch': [
      'error',
      {
        emptyDefaultCase: 'do-nothing-comment',
      },
    ],
    'unicorn/filename-case': [
      'error',
      {
        cases: {
          camelCase: true,
          pascalCase: true,
          snakeCase: true,
          kebabCase: true,
        },
        ignore: ['react-native-compass.d.ts'],
      },
    ],
  },
  settings: {
    'import/resolver': {
      typescript: {},
    },
    react: {
      version: 'detect',
    },
    jest: {
      version: '^29.2.1',
    },
  },
  ignorePatterns: [
    'metro.config.js',
    'node_modules/*',
    'jest.config.js',
    'babel.config.js',
    'android/*.mp3',
    'ios/*.caf',
  ],
};
