/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

import React, { useCallback, useEffect, useState } from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import RootNavigation from './src/navigation/RootNavigation';
import { navigate, navigationRef } from './src/navigation/rootNavigatorRef';
import notifee, { EventType } from '@notifee/react-native';
import { InteractionManager, Linking, Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import colors from './src/constants/colors';

import { Constants, shareAppAndroid, shareAppIos } from './src/constants/constants';
import DeviceInfo from 'react-native-device-info';
import Modal from 'react-native-modal';

const App: React.FC = () => {
  const [showForceUpdate, setShowForceUpdate] = useState(false);
  const [showCancelButton, setShowCancelButton] = useState(false);
  const [appUpdationMessage, setAppUpdationMessage] = useState('');

  const checkAppVersion = useCallback(async () => {
    const url = `${Constants.updateUrl}`;
    try {
      const response = await fetch(url);
      const data = await response.json();
      const { prayer_app } = data;
      if (Platform.OS === 'ios') {
        if (prayer_app.iOS.forceUpdate) {
          setShowCancelButton(false);
        } else {
          setShowCancelButton(true);
        }
        if (prayer_app.iOS.currentVersion > DeviceInfo.getVersion()) {
          setShowForceUpdate(true);
        } else {
          setShowForceUpdate(false);
        }
        setAppUpdationMessage(prayer_app.iOS.message);
      } else {
        if (prayer_app.android.forceUpdate) {
          setShowCancelButton(false);
        } else {
          setShowCancelButton(true);
        }
        if (prayer_app.android.currentVersion > DeviceInfo.getVersion()) {
          setShowForceUpdate(true);
        } else {
          setShowForceUpdate(false);
        }
        setAppUpdationMessage(prayer_app.android.message);
      }
    } catch (error) {
      console.error('Error occurred:', error);
      throw error;
    }
  }, []);

  useEffect(() => {
    checkAppVersion();
  }, [checkAppVersion]);

  const RetryNavigation = useCallback(
    (navigationAction: () => void, retries = 5, delay = 500) => {
      if (navigationRef.isReady()) {
        navigationAction();
      } else if (retries > 0) {
        setTimeout(
          () => RetryNavigation(navigationAction, retries - 1, delay),
          delay,
        );
      }
    },
    [],
  );

  notifee.onBackgroundEvent(async ({ type, detail }) => {
    if (
      type === EventType.PRESS &&
      detail.pressAction?.id === 'default' &&
      detail.notification?.title?.includes('Adhan Time')
    ) {
      RetryNavigation(() => navigate('prayer'));
    }
  });

  useEffect(() => {
    const unsubscribe = notifee.onForegroundEvent(({ type, detail }) => {
      if (
        type === EventType.PRESS &&
        detail.pressAction?.id === 'default' &&
        detail.notification?.title?.includes('Adhan Time')
      ) {
        InteractionManager.runAfterInteractions(() => {
          navigate('prayer');
        });
      }
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    (async () => {
      const initialNotification = await notifee.getInitialNotification();
      if (initialNotification?.notification?.title?.includes('Adhan Time')) {
        RetryNavigation(() => navigate('prayer'));
      }
    })();
  }, [RetryNavigation]);

  // while app is open in background
  const onClickURL = () => {
    Linking.addEventListener('url', ({ url }) => {
      const screenName = url.split('//')[1];
      if (screenName) {
        // navigate(screenName);
        navigate('prayer');
      }
    });
    return () => {
      Linking.removeAllListeners('url');
    };
  };

  // while app is closed
  const onInitialURL = async () => {
    const initialURL = await Linking.getInitialURL();
    if (initialURL) {
      const screenName = initialURL.split('//')[1];
      if (screenName) {
        setTimeout(() => {
          // navigate(screenName);
          navigate('prayer');
        }, 1000);
      }
    }
  };

  useEffect(()=>{
    onClickURL();
    onInitialURL();
  }, []);

  

  const handleUpdate = () => {
    setShowForceUpdate(false);
    const appStoreUrl =
      Platform.OS === 'ios'
        ? shareAppIos
        : shareAppAndroid;

    Linking.openURL(appStoreUrl).catch(err =>
      console.error('Failed to open store:', err),
    );
  };

  return (
    <View style={{flex: 1}}>
      <SafeAreaProvider style={{ backgroundColor: colors.alimDarkBg }}>
        <RootNavigation />
        <Modal isVisible={showForceUpdate}>
          <View
            style={[
              styles.modalContainer,
              { backgroundColor: colors.alimLightBlue },
            ]}>
            <Text
              style={[styles.modalTitle, { color: colors.primaryWhite }]}>
              Update Available
            </Text>
            <Text
              style={[styles.modalMessage, { color: colors.primaryWhite }]}>
              {appUpdationMessage}
            </Text>
            <View
              style={[
                styles.buttonView,
                {
                  backgroundColor: colors.primaryWhite,
                },
              ]}>
              <TouchableOpacity
                style={{ justifyContent: 'center' }}
                onPress={handleUpdate}>
                <Text
                  style={{
                    color: colors.alimDarkBg,
                    textAlign: 'center',
                    fontSize: 18,
                  }}>
                  Update Now
                </Text>
              </TouchableOpacity>
            </View>

            {showCancelButton && (
              <View
                style={[
                  styles.buttonView,
                  {
                    backgroundColor: colors.primaryWhite,
                    top: 24,
                  },
                ]}>
                <TouchableOpacity
                  style={{ justifyContent: 'center' }}
                  onPress={() => {
                    setShowForceUpdate(false);
                  }}>
                  <Text
                    style={{
                      color: colors.alimDarkBg,
                      textAlign: 'center',
                      fontSize: 18,
                    }}>
                    Cancel
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </Modal>
      </SafeAreaProvider>
    </View>
  );
};

export default App;

const styles = StyleSheet.create({
  modalContainer: {
    padding: 30,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 50,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonView: {
    borderRadius: 25,
    width: '95%',
    height: 50,
    justifyContent: 'center',
  },
});