/**
 * @format
 */

import React from 'react';
import {AppRegistry, View, ActivityIndicator} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import store from './src/redux/store/store';
import colors from './src/constants/colors';
import InternetListener from './src/providers/InternetListener';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import {persistStore} from 'redux-persist';
import {gestureHandlerRootHOC} from 'react-native-gesture-handler';

const persistor = persistStore(store);

const ReduxApp = () => {
  return (
    <Provider store={store}>
      <PersistGate
        loading={
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: colors.alimDarkBg,
            }}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        }
        persistor={persistor}>
        <InternetListener>
          <App />
        </InternetListener>
      </PersistGate>
    </Provider>
  );
};

AppRegistry.registerComponent(appName, () => gestureHandlerRootHOC(ReduxApp));
