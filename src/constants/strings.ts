const appName = 'alimprayer';
const PrayerConstants = {
  prayerNoNetworkText: 'offline: Turn on internet for changing location',
  prayerViewNoNetworkText: 'offline: Place name could not be found',
  locationEnableAlert:
    "To continue using this service, we require your permission to access your device's location.",
  notificationTooltipText:
    'Never miss your prayers. Get notified by enabling prayer notifications here.',
  prayerSettingsTooltipText: 'Adjust your prayer settings here.',
  calenderTooltipText:
    'Select your desired date to get prayer timing of that date.',
  locationTooltipText: 'Get prayer times of any location by searching here.',
};
const networkConstants = {
  networkErrorTitle: 'No internet connection',
  networkErrorDesc:
    'It appears that there is no internet connection. Please check your internet connection and try again.',
  ok: 'ok',
};

export default {appName, PrayerConstants, networkConstants};
