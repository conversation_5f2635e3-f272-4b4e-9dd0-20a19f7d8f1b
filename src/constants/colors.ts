const colors = {
  primaryWhite: '#ffffff',
  primaryWhiteHeading: '#ffffffBF',
  primaryBlack: '#000000',
  primaryAlimBlack: '#3E4968',
  settingsTextColor: '#DDDDDD',
  backIconBgColor: '#151B26',
  grey: '#808080',
  closeColor: '#D3D3D3',
  red: '#FF0000',
  transparent: '#00000000',
  arabicGray: '#FFFFFF85',
  blurColor: '#00000080',
  buttonBgColor: '#FFFFFF20',
  selectedItemColor: '#ffffff18',
  alimDarkBg: '#0D142C',
  alimLightBg: '#252b41',
  alimButtonRed: '#C72D3C',
  tabHeaderColor: '#FFFFFF33',
  quranTypeColor: '#E1E1E1',
  moreTextColor: '#C6C6C6',
  lightWhiteColor: '#DDDDDD',
  lightWhiteSmallTitleColor: '#D3D3D3',
  maxSliderColor: '#8888884C',
  darkmode: '#0a090c',
  lightmode: '#191a1f',
  aqua: '#27959A',
  orange: '#FE8A07',
  green: '#20C961',
  moreIconColor: '#7D7D7D',
  subText: '#D3D3D3',
  footnoteTitle: 'blue',
  modeColorLight: '#3C3C52',
  modeColorDark: '#242737',
  welcomeText: '#DADADA',
  alimLightBlue: '#1F3352',
  searchbarBgColor: '#252836',
  subTextHadith: '#D6D6D6',
  reporterColor: '#D1D1D1',
  whiteSemiTransparent: '#FFFFFF80',
  openingText: '#FFFFFF50',
  pickerYellow: '#FF9C00',
  blue: '#0000FF',
  repeatTrackColor: '#326fa8',
  slider: '#AAA8A8',
  searchView: '#404040',
  searchBar: '#FFFFFF0D',
  bookName: '#FFFFFF54',
  lightBox: '#F2F2F2',
  lightSelection: '#435773',
  magneticColor: '#393939',
  remindBlack: '#26262C',
  lightBlue: '#1F335255',
  subTextcolor: '#FFFFFFBF',
  subTextLightcolor: '#0D142CBF',
};

export default colors;
