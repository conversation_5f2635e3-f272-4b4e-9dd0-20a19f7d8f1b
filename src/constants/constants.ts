import { Dimensions } from 'react-native';
import {
  widthPercentageToDP,
  heightPercentageToDP,
} from 'react-native-responsive-screen';

export const persistenceApiHost = 'https://www.alim.org/';
export const donateUsUrl = `${persistenceApiHost}donate`;
export const fetchLocationUrl =
  'https://nominatim.openstreetmap.org/reverse?format=json&lat=';
export const timeZoneGoogleUrl =
  'https://maps.googleapis.com/maps/api/timezone/json?location=';
export const otherAppIos =
  'https://apps.apple.com/us/developer/the-alim-foundation-inc/id891247152';
export const otherAppAndroid =
  'https://play.google.com/store/apps/developer?id=The+Alim+Foundation+Inc.';
export const shareAppIos =
  'https://itunes.apple.com/us/app/alim-prayer-minder/id1313267368?ls=1&mt=8';
export const shareAppAndroid =
  'https://play.google.com/store/apps/details?id=com.alim.prayerminder';
export const wp = (val: number) => widthPercentageToDP(val);

export const hp = (val: number) => heightPercentageToDP(val);

export const { height, width } = Dimensions.get('window');

export const Constants = {
  updateUrl: 'https://astro-alim-devl.alim.org/api/force-update/data.json',
  qiblaMageneticDetectText:
    'Swing your mobile swiftly several times in a figure 8 motion. Take your smart phone out of its cover if the cover contains magnet.\n\n\n Ensure that the device is away from metal or avoid placing it over metallic surfaces which may affect the accuracy.',
  prayerNoNetworkText: 'offline: Turn on internet for changing location',
  prayerViewNoNetworkText: 'offline: Place name could not be found',
};
