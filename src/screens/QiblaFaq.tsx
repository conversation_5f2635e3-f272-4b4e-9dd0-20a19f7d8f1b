import {
  View,
  StyleSheet,
  Text,
  FlatList,
  Platform,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import React, { useLayoutEffect, useMemo } from 'react';
import Icon from 'react-native-vector-icons/Ionicons';
import colors from '../constants/colors';
import qiblaFaq from '../data/qiblaFaq';
import { RootStackScreenProps } from '../types/types';

interface SubItem {
  subTitle: string;
  info: string;
}

interface MainItem {
  id: number;
  title: string;
  data: string;
  subData?: SubItem[];
}

const font = Platform.OS === 'ios' ? 'Quicksand-Regular' : 'QuicksandRegular';
const fontBold = 'Quicksand-Medium';

const QiblaFaq: React.FC<RootStackScreenProps<'qiblaFaq'>> = ({
  navigation,
}) => {
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();

  const headerLeft = useMemo(
    () => (
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Icon name='chevron-back' size={24} style={styles.backBtn} />
      </TouchableOpacity>
    ),
    [navigation],
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerTitle: 'Qibla FAQ',
      headerStyle: {
        backgroundColor: colors.alimLightBlue,
      },
      headerTintColor: colors.primaryWhite,
      headerTitleAlign: 'center',
      headerLeft: () => headerLeft,
    });
  }, [ headerLeft, navigation]);

  const renderItem = ({ item }: { item: MainItem }) => {
    return (
      <View>
        <Text style={styles.titleText}>{item.title}</Text>
        <View>
          <Text style={styles.subTitle}>{item.data}</Text>
          {item.subData &&
            item.subData.map((subItem: SubItem) => (
              <View key={subItem.subTitle} style={styles.subItemContainer}>
                <Text style={styles.subTitle}>{subItem.subTitle}</Text>
                <Text style={styles.subTitle}>{subItem.info}</Text>
              </View>
            ))}
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { width: screenWidth, height: screenHeight }]}>
      <FlatList
        data={qiblaFaq}
        renderItem={renderItem}
        showsVerticalScrollIndicator={false}
        keyExtractor={item => item.id.toString()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    backgroundColor: colors.alimDarkBg,
    flex: 1,
  },
  subItemContainer: {
    marginTop: 10,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: font,
    color: colors.primaryWhite,
  },
  titleText: {
    fontSize: 17,
    paddingVertical: 15,
    fontFamily: fontBold,
    color: colors.primaryWhite,
  },
  backBtn: {
    color: colors.primaryWhite,
  },
});

export default QiblaFaq;
