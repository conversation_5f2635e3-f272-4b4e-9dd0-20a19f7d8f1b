import {
  FlatList,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Alert,
  ImageBackground,
  ScrollView,
  AppState,
  AppStateStatus,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import moment from 'moment';
import {
  PERMISSIONS,
  RESULTS,
  check,
  openSettings,
  request,
} from 'react-native-permissions';
import Geolocation from '@react-native-community/geolocation';
import { promptForEnableLocationIfNeeded } from 'react-native-android-location-enabler';
import DeviceInfo from 'react-native-device-info';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import armoment from 'moment-hijri';
import { StackNavigationProp } from '@react-navigation/stack';
import Feather from 'react-native-vector-icons/Feather';
import Modal from 'react-native-modal';
import {
  GooglePlacesAutocomplete,
  GooglePlacesAutocompleteRef,
} from 'react-native-google-places-autocomplete';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import Config from 'react-native-config';
import { prayerData } from '../data/prayerData';
import colors from '../constants/colors';
import PrayerService from '../service/prayerService';
import { useAppDispatch, useAppSelector } from '../redux';
import strings from '../constants/strings';
import { fetchLocationUrl } from '../constants/constants';
import {
  saveLocation,
  storeNotifAdjustments,
  storeCalculationSettings,
  storeNotfnSettings,
  saveLocationHistory,
} from '../redux/slices/prayerSlice';
import { getCalculationMethod } from '../utils/Prayerutils';
import notifService from '../service/notification';
import { hp, wp, width } from '../constants/constants';
import { goBack, navigate } from '../navigation/rootNavigatorRef';
import { fontFamily, fontSize } from '../utils/fontUtils';
import 'moment/locale/ar';
import WidgetComponent from '../components/WidgetComponent';
import { TMainStackProps } from '../types/types';
import Picker, { PickerHandle } from '../components/Picker';
import About from './About';

interface PrayerProps {
  navigation: StackNavigationProp<TMainStackProps, 'prayer'>;
}
const Prayer: React.FC<PrayerProps> = ({ navigation }) => {
  const prayerService = new PrayerService();
  const isFocused = useIsFocused();
  const dispatch = useAppDispatch();
  const {
    calculation,
    adjustments,
    notification,
    notifAdjustments,
    searchLocationHistory,
  } = useAppSelector(state => state.prayer.prayerSettings);
  const isAboutVisible = useAppSelector(state => state.prayer.isAboutVisible);
  const { isConnected } = useAppSelector(state => state.internet);
  const [selDate, setSelectedDate] = useState(moment().locale('en'));
  armoment.locale('ar');
  const [selDateHijri, setDateHijri] = useState(armoment());
  const {
    FajrReminder,
    SunriseReminder,
    ZuhrReminder,
    AsrReminder,
    MaghribReminder,
    IshaReminder,
  } = notification;
  const [currentPrayerName, setPrayerName] = useState('');
  const [remainingHr, setRemainingHr] = useState(0);
  const [remainingMin, setRemainingMin] = useState(0);
  const [nextPrayerName, setNextPrayerName] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [ModalTitle, setModalTitle] =
    useState<keyof typeof adjustments>('Fajr');
  const [selectedMinIndex, setSelectedMinIndex] = useState(0);
  const [selectedItem, setSelectedItem] = useState('Before');
  const iconMap: { [key: string]: any } = {
    Isha: require('../assets/images/playerIcons/Isha.png'),
    Maghrib: require('../assets/images/playerIcons/Maghreb.png'),
    Asr: require('../assets/images/playerIcons/Asr.png'),
    Zuhr: require('../assets/images/playerIcons/Dhur.png'),
    Fajr: require('../assets/images/playerIcons/Fajr.png'),
  };
  const prayerSettings = useAppSelector(state => state.prayer.prayerSettings);
  const timeData = Array.from({ length: 10 }, (_, i) => i);
  const timingOptions = ['Before', 'After'];
  const timeDataRef = useRef<PickerHandle>(null);
  const timeOptionsRef = useRef<PickerHandle>(null);
  const [showSeachLocation, setShowSeachLocation] = useState(false);
  const locationRef = useRef<GooglePlacesAutocompleteRef | null>(null);
  const previousAppState = useRef<AppStateStatus>(AppState.currentState);
  const [hasDeclinedPrompt, setHasDeclinedPrompt] = useState(false);
  const [showLocationAlert, setShowLocationAlert] = useState(true);
  const isPlatformAndroid = Platform.OS === 'android';
  const { width: screenWidth, height: screenHeight } = useWindowDimensions();

  const getPrayerEnabled = (prayerName: string) => {
    switch (prayerName) {
      case 'Fajr':
        return FajrReminder;
      case 'Zuhr':
        return ZuhrReminder;
      case 'Asr':
        return AsrReminder;
      case 'Maghrib':
        return MaghribReminder;
      case 'Isha':
        return IshaReminder;
      case 'Sunrise':
        return SunriseReminder;
      default:
        false;
    }
  };
  const scheduleReminder = async (settings: any) => {
    if (isPlatformAndroid) {
      prayerService.scheduleReminder(settings);
    } else {
      prayerService.iosScheduleAction(settings);
    }
  };

  const togglePrayerSwitch = async (
    prayerName: string,
    newAdjustments: any,
    value?: boolean,
  ) => {
    const settings: any = {
      ...notification,
      ...calculation,
      adjustments: newAdjustments,
    };
    const toggledStatus = value ?? !getPrayerEnabled(prayerName);
    settings[`${prayerName}Reminder`] = toggledStatus;
    scheduleReminder(settings);
    const update: any = {};
    update[`${prayerName}Reminder`] = toggledStatus;
    dispatch(storeNotfnSettings(update));
  };

  const toggleModal = useCallback(
    (modalTitle: keyof typeof adjustments) => {
      console.log('=========modalTitle==============',modalTitle);
      setIsModalVisible(!isModalVisible);
      setModalTitle(modalTitle);
      if (typeof notifAdjustments[modalTitle] === 'number') {
        setSelectedMinIndex(Math.abs(notifAdjustments[modalTitle] as number));
        setSelectedItem(
          (notifAdjustments[modalTitle] as number) < 0 ? 'Before' : 'After',
        );
        timeDataRef.current?.scrollToTargetIndex(
          Math.abs(notifAdjustments[modalTitle]),
        );
        timeOptionsRef.current?.scrollToTargetIndex(
          (notifAdjustments[modalTitle] as number) < 0 ? 0 : 1,
        );
      }
    },
    [isModalVisible, notifAdjustments],
  );

  const checkAndRequestAndroidPermission = async (
    onPermissionGranted: () => void,
    onPermissionDenied: () => void,
  ) => {
    if (Number(DeviceInfo.getSystemVersion()) >= 13) {
      const enabled = await notifService.checkPermission();
      if (enabled) {
        onPermissionGranted();
      } else {
        const _request = await notifService.requestPermission();
        if (_request) {
          onPermissionGranted();
        } else {
          onPermissionDenied();
        }
      }
    } else {
      onPermissionGranted();
    }
  };

  const checkAndRequestIOSPermission = async (
    onPermissionGranted: () => void,
    onPermissionDenied: () => void,
  ) => {
    PushNotificationIOS.checkPermissions(async permissions => {
      if (permissions.alert || permissions.badge || permissions.sound) {
        onPermissionGranted();
      } else {
        try {
          const result = await PushNotificationIOS.requestPermissions({
            alert: true,
            badge: true,
            sound: true,
          });
          if (result.alert || result.badge || result.sound) {
            onPermissionGranted();
          } else {
            onPermissionDenied();
          }
        } catch (error) {
          console.error('Error requesting permissions:', error);
          onPermissionDenied();
        }
      }
    });
  };

  const prayerTimeItem = ({
    item,
  }: {
    item: Exclude<keyof typeof adjustments, 'Enabled'>;
    index: number;
  }) => {
    const iconSource = iconMap[item];
    return (
      <View>
        <View style={styles.prayerType}>
          <Image
            source={iconSource}
            style={styles.image}
            tintColor={colors.primaryWhite}
          />
          <View style={styles.prayerTitle}>
            <Text
              style={[
                styles.prayerTitleText,
                { color: colors.primaryWhite, paddingLeft: 5 },
              ]}
            >
              {item}
            </Text>
          </View>
          <View style={[styles.prayerTimeView, { width: screenWidth * 0.28 }]}>
            <Text
              style={[
                styles.prayerTimeText,
                { color: colors.primaryWhite, paddingLeft: 10 },
              ]}
            >
              {prayerService.getPrayerTime(
                calculation,
                adjustments,
                item,
                selDate,
              )}
            </Text>
          </View>
          <TouchableOpacity
            style={{
              borderRadius: 20,
              padding: 5,
              backgroundColor: notification[`${item}Reminder`]
                ? colors.alimButtonRed
                : colors.remindBlack,
            }}
            onPress={() => {
              const handlePermissionGranted = () => {
                toggleModal(item);
              };
              const handlePermissionDenied = () => {
                Alert.alert(
                  'Notification permission required',
                  'To continue using this service, we require your permission to access your notifications.',
                  [
                    {
                      text: 'Go to settings',
                      onPress: () => {
                        openSettings();
                      },
                      style: 'default',
                    },
                    {
                      text: 'Cancel',
                      style: 'cancel',
                    },
                  ],
                );
              };
              if (notification[`${item}Reminder`]) {
                console.log('====================================');
                console.log('inisde if');
                console.log('====================================');
                const isEnabled = Object.entries(notifAdjustments ?? {})
                  .filter(([key]) => key !== 'Enabled' && key !== item)
                  .some(([, value]) => value !== 0);
                const update = {
                  Enabled: isEnabled,
                  [item]: 0,
                };
                dispatch(storeNotifAdjustments(update));
                const newAdjustments = {
                  ...notifAdjustments,
                  Enabled: isEnabled,
                  [ModalTitle]: 0,
                };
                togglePrayerSwitch(item, newAdjustments);
              } else {
                console.log('inisde else');
                if (isPlatformAndroid) {
                  checkAndRequestAndroidPermission(
                    handlePermissionGranted,
                    handlePermissionDenied,
                  );
                } else {
                  checkAndRequestIOSPermission(
                    handlePermissionGranted,
                    handlePermissionDenied,
                  );
                }
              }
            }}>
            <Text style={[styles.remindText, { color: colors.primaryWhite }]}>
              Remind me
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderItem = ({
    item,
  }: {
    item: 'Sunrise' | 'Sunset';
    index: number;
  }) => {
    return (
      <View style={[styles.renderContainer, { width: screenWidth * 0.45 }]}>
        <Image
          source={
            item === 'Sunrise'
              ? require('../assets/images/playerIcons/Fajr.png')
              : require('../assets/images/playerIcons/Maghreb.png')
          }
          style={styles.largeImage}
          tintColor={colors.primaryWhite}
        />
        <View>
          <Text style={[styles.titleText, { color: colors.primaryWhite }]}>
            {item}
          </Text>

          <Text style={[styles.prayerTimeText, { color: colors.primaryWhite }]}>
            {prayerService.getPrayerTime(
              calculation,
              adjustments,
              item,
              selDate,
            )}
          </Text>
        </View>
      </View>
    );
  };

  const fetchLocationAddress = useCallback(
    async (long: string, lat: string) => {
      try {
        const response = await fetch(`${fetchLocationUrl}${lat}&lon=${long}`);
        if (response.ok) {
          const data = await response.json();
          const cityOrTown =
            data.address.city ??
            data.address.town ??
            data.address.village ??
            '';
          const loc = `${cityOrTown}, ${data.address.state}`;
          const locationDict = {
            location: loc,
            lat,
            lng: long,
          };
          dispatch(saveLocation(locationDict));
          const result = await getCalculationMethod(lat, long);
          if (result) {
            const { calcMethod, timeZoneId } = result;
            dispatch(
              storeCalculationSettings({
                Method: calcMethod,
                Timezone: timeZoneId,
              }),
            );
          }
        }
      } catch (error) {}
    },
    [dispatch],
  );

  const getCurrentLocation = useCallback(() => {
    Geolocation.getCurrentPosition(
      (position: any) => {
        const long: string = JSON.stringify(position.coords.longitude);
        const lat: string = JSON.stringify(position.coords.latitude);
        fetchLocationAddress(long, lat);
      },
      (_error: unknown) => {},
      {},
    );
  }, [fetchLocationAddress]);

  React.useEffect(() => {
    const initLocationPermission = async () => {
      const permissionStatus = await check(
        Platform.select({
          android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
          default: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
        }),
      );

      if (permissionStatus === RESULTS.GRANTED && isConnected) {
        getCurrentLocation();
      }
    };

    initLocationPermission();
  }, [getCurrentLocation, isConnected]);

  useFocusEffect(
    useCallback(() => {
      setHasDeclinedPrompt(false);
    }, []),
  );

  const checkLocationServices = useCallback(async () => {
    if (isPlatformAndroid) {
      if (hasDeclinedPrompt) {
        return;
      }

      try {
        const enableResult = await promptForEnableLocationIfNeeded();
        if (enableResult === 'enabled') {
          getCurrentLocation();
        }
      } catch (error) {
        if (error instanceof Error) {
          setHasDeclinedPrompt(true);
          navigation.goBack();
        }
      }
    }
  }, [getCurrentLocation, hasDeclinedPrompt, isPlatformAndroid, navigation]);

  const requestLocationPermission = useCallback(async () => {
    try {
      const isLocationEnabled = await DeviceInfo.isLocationEnabled();
      if (!isLocationEnabled) {
        checkLocationServices();
      } else {
        const permissionStatus = await check(
          Platform.select({
            android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
            ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
            default: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          }),
        );

        if (
          permissionStatus === RESULTS.DENIED ||
          permissionStatus === RESULTS.BLOCKED ||
          permissionStatus === RESULTS.UNAVAILABLE
        ) {
          if (showLocationAlert) {
            if (isPlatformAndroid) {
              setShowLocationAlert(false);
            }
            const requestResult = await request(
              Platform.select({
                android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
                ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
                default: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
              }),
            );

            if (
              requestResult === RESULTS.GRANTED &&
              calculation.location.includes('Default')
            ) {
              getCurrentLocation();
            } else {
              Alert.alert(
                'Enable location access',
                strings.PrayerConstants.locationEnableAlert,
                [
                  {
                    text: 'Go to settings',
                    onPress: () => {
                      if (isPlatformAndroid) {
                        setTimeout(() => {
                          setShowLocationAlert(true);
                        }, 500);
                      }
                      openSettings();
                    },
                    style: 'cancel',
                  },
                  {
                    text: 'Cancel',
                    onPress: () => {
                      goBack();
                      if (isPlatformAndroid) {
                        setShowLocationAlert(true);
                      }
                    },
                    style: 'cancel',
                  },
                ],
              );
            }
          }
        } else if (
          permissionStatus === RESULTS.GRANTED &&
          calculation.location.includes('Default')
        ) {
          getCurrentLocation();
        }
      }
    } catch (error) {
      console.error('Error checking/requesting location permission:', error);
    }
  }, [
    calculation.location,
    checkLocationServices,
    getCurrentLocation,
    isPlatformAndroid,
    showLocationAlert,
  ]);

  useFocusEffect(
    useCallback(() => {
      isFocused && requestLocationPermission();
      isFocused && isConnected !== null &&
        !isConnected &&
        calculation.location.includes('Default') &&
        Alert.alert(
          'WARNING',
          'Offline : Turn on internet for changing location',
          [
            {
              text: 'Ok',
              onPress: () => {
                goBack();
              },
              style: 'cancel',
            },
          ],
        );
      setSelectedDate(moment().locale('en'));
      setDateHijri(armoment());
    }, [
      calculation.location,
      isConnected,
      isFocused,
      requestLocationPermission,
    ]),
  );

  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (
        previousAppState.current === 'background' &&
        nextAppState === 'active' &&
        isFocused
      ) {
        requestLocationPermission();
      }
      previousAppState.current = nextAppState;
    };
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => {
      subscription.remove();
    };
  }, [isFocused, requestLocationPermission]);

  const handleNextDay = () => {
    setSelectedDate(prevDate => prevDate.clone().add(1, 'day'));
    setDateHijri(prevDate => prevDate.clone().add(1, 'day'));
  };

  const handlePrevDay = () => {
    setSelectedDate(prevDate => prevDate.clone().subtract(1, 'day'));
    setDateHijri(prevDate => prevDate.clone().subtract(1, 'day'));
  };

  const getNextPrayer = useCallback(() => {
    const settings: any = { ...notification, ...calculation, adjustments };
    const nextPrayer = new PrayerService().getNextPrayer(settings);
    const remainingInSeconds = Math.floor(nextPrayer.remaining / 1000);
    const hours = Math.floor(remainingInSeconds / 3600);
    const minutes = Math.floor((remainingInSeconds % 3600) / 60);
    setRemainingHr(hours);
    setRemainingMin(minutes);
    setPrayerName(nextPrayer.currentPrayer);
    setNextPrayerName(nextPrayer.name);
  }, [adjustments, calculation, notification]);

  React.useEffect(() => {
    getNextPrayer();
  }, [getNextPrayer]);

  const flatlistHeader = () => (
    <>
      <TouchableOpacity
        style={[
          styles.popUpTitlecontainer,
          { backgroundColor: colors.alimDarkBg },
        ]}
        onPress={() => {
          getCurrentLocation();
          setShowSeachLocation(!showSeachLocation);
        }}>
        <View style={styles.locationIconContainer}>
          <Image
            source={require('../assets/images/locate_me.png')}
            style={[styles.locateMeImage, styles.alignCenter]}
          />
        </View>
        <Text style={styles.locateMeText}>Use current location</Text>
      </TouchableOpacity>
      {searchLocationHistory.length > 0 && (
        <View style={[styles.separator, { width: screenWidth }]} />
      )}
    </>
  );

  const listHeader = () => (
    <>
      <View style={styles.headerContainer}>
        <View style={styles.header}>
          <Text
            style={[
              styles.headerText,
              { paddingLeft: 20, color: colors.primaryWhite },
            ]}
          >
            Prayer{' '}
          </Text>
        </View>
        <View style={[styles.timeTitle, { width: screenWidth * 0.45 }]}>
          <Text
            style={[
              styles.headerText,
              { paddingRight: 0, color: colors.primaryWhite },
            ]}
          >
            Time
          </Text>
        </View>
      </View>
      <View style={[styles.separator, { width: screenWidth }]} />
    </>
  );
  return (
    <ScrollView
      style={[
        styles.container,
        {
          backgroundColor: colors.alimDarkBg,
        },
      ]}
      keyboardShouldPersistTaps="handled"
      bounces={false}>
      <ImageBackground
        source={require('../assets/images/appbar.png')}
        style={[
          styles.appBar,
          { width: screenWidth, height: screenHeight / 4 },
        ]}
        imageStyle={styles.appBarBackgroundImage}
        resizeMode="cover">
        <View
          style={[
            styles.appbarInfoContainer,
            {
              width: screenWidth * 0.9,
            },
          ]}>
          <View style={[styles.currentPrayerContainer, { width: screenWidth }]}>
            <View>
              <Text style={styles.currentPrayerName}>{currentPrayerName}</Text>
              <Text style={styles.remainingTime}>
                {remainingHr} hrs {remainingMin} min until {nextPrayerName}
              </Text>
            </View>
          </View>
          <View
            style={[styles.locationContainer, { height: screenHeight / 8 }]}>
            <Text style={styles.locationStyle}>Location</Text>
            <TouchableOpacity
              onPress={() => setShowSeachLocation(!showSeachLocation)}>
              <Text
                style={[
                  styles.locationStyle,
                  { width: screenWidth / 2, textAlign: 'right' },
                ]}
                ellipsizeMode="tail"
                numberOfLines={1}>
                {prayerSettings.calculation.location}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ImageBackground>
      <View style={{ marginTop: -screenHeight / 20 }}>
        <View style={[styles.dateContainer, { width: screenWidth - wp(20), height: Platform.OS === 'ios' && Platform.isPad ? 70 : 50 }]}>
          <Feather
            name="chevron-left"
            size={hp(3)}
            color={colors.primaryWhite}
            style={styles.icon}
            onPress={() => handlePrevDay()}
          />
          <View style={styles.dateSubContainer}>
            <Text
              style={[
                styles.date,
                {
                  color: colors.primaryWhite,
                },
              ]}>
              {selDate.local().format('MMMM Do YYYY')}
            </Text>
            <Text
              style={[
                styles.arabicDate,
                {
                  color: colors.primaryWhite,
                },
              ]}>
              {`${selDateHijri.format('iMMMM')} ${selDateHijri.iDate()} ${selDateHijri.iYear()}`}
            </Text>
          </View>
          <Feather
            name="chevron-right"
            size={hp(3)}
            color={colors.primaryWhite}
            style={styles.icon}
            onPress={() => handleNextDay()}
          />
        </View>
        <View
          style={[
            styles.flatlistBox,
            {
              backgroundColor: colors.alimLightBlue,
              height: Platform.OS === 'ios' && Platform.isPad ? 300 : 255,
            },
          ]}>
          <View>
            <FlatList
              data={prayerData.prayerList}
              scrollEnabled={false}
              keyExtractor={(_, index) => index.toString()}
              renderItem={({ item, index }) => prayerTimeItem({ item, index })}
              ListHeaderComponent={listHeader}
            />
          </View>
        </View>
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            width: screenWidth,
          }}>
          <View
            style={[
              styles.flatlistSubContainer,
              {
                backgroundColor: colors.alimLightBlue,
                width: screenWidth * 0.9,
              },
            ]}>
            <FlatList
              data={['Sunrise', 'Sunset']}
              scrollEnabled={false}
              horizontal
              keyExtractor={(_, index) => index.toString()}
              style={{ width: screenWidth * 0.9 }}
              renderItem={renderItem}
            />
          </View>
        </View>
        <Text
          style={[
            styles.headerStyle,
            {
              paddingLeft: Platform.OS === 'ios' && Platform.isPad ? 25 : 10,
              color: colors.primaryWhite,
              top: 10,
            },
          ]}>
          Prayer Qibla
        </Text>
        <WidgetComponent
          backgroundColor={colors.alimButtonRed}
          titleText="Click To View Qibla"
          descriptionText="Pray In The Right Direction"
          footerText="Masjid Al-Haram"
          imageSource={require('../assets/images/kabah.png')}
          onItemPress={() => {
            if (isConnected) {
              navigation.navigate('qiblaCompass');
            } else {
              Alert.alert(
                'Warning',
                'Offline : Turn on internet for qibla direction',
                [
                  {
                    text: 'Ok',
                    onPress: () => {},
                    style: 'cancel',
                  },
                ],
              );
            }
          }}
        />
      </View>
      {calculation.location.includes('Default') && (
        <ActivityIndicator size="large" />
      )} 
      <Modal
        isVisible={isModalVisible}
        onBackdropPress={() => {
          setIsModalVisible(!isModalVisible);
        }}
        backdropOpacity={0.4}
        style={styles.alignCenter}>
        <View style={styles.popUp}>
          <View style={styles.popUpTitlecontainer}>
            <Image
              source={iconMap[ModalTitle]}
              style={{ width: 24, height: 24, marginRight: 10 }}
            />
            <Text
              style={{
                color: colors.primaryWhite,
                fontSize: 20,
                fontWeight: 'bold',
              }}>
              {ModalTitle}
            </Text>
          </View>
          <View style={styles.popUpContentContainer}>
            <View style={styles.minSelectorContainer}>
              <Picker
                ref={timeDataRef}
                dataSource={timeData as never[]}
                selectedIndex={timeData.findIndex(
                  item => item === selectedMinIndex,
                )}
                renderItem={(data, index) => {
                  const isSelected = index === selectedMinIndex;
                  return (
                    <Text
                      style={{
                        color: isSelected ? colors.primaryWhite : colors.grey,
                      }}>
                      {data} minutes
                    </Text>
                  );
                }}
                onValueChange={index => {
                  setSelectedMinIndex(index);
                }}
                userWidth={width / 3}
                userBackgroundColor={colors.alimDarkBg}
              />
              <Picker
                ref={timeOptionsRef}
                dataSource={timingOptions as never[]}
                selectedIndex={timingOptions.findIndex(
                  item => item === selectedItem,
                )}
                renderItem={data => {
                  const isSelected = data === selectedItem;
                  return (
                    <Text
                      style={{
                        color: isSelected ? colors.primaryWhite : colors.grey,
                      }}>
                      {data}
                    </Text>
                  );
                }}
                onValueChange={value => {
                  setSelectedItem(value);
                }}
                userWidth={width / 3}
                userBackgroundColor={colors.alimDarkBg}
              />
            </View>
            <TouchableOpacity
              style={styles.remindMeButton}
              onPress={() => {
                const update = {
                  Enabled: true,
                  [ModalTitle]:
                    selectedItem === 'Before'
                      ? -timeData[selectedMinIndex]
                      : timeData[selectedMinIndex],
                };
                dispatch(storeNotifAdjustments(update));
                const newAdjustments = {
                  ...notifAdjustments,
                  Enabled: true,
                  [ModalTitle]:
                    selectedItem === 'Before'
                      ? -timeData[selectedMinIndex]
                      : timeData[selectedMinIndex],
                };
                setIsModalVisible(!isModalVisible);
                togglePrayerSwitch(ModalTitle, newAdjustments);
              }}>
              <Text style={styles.remindMeButtontext}>Remind me</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <Modal
        isVisible={showSeachLocation}
        onBackdropPress={() => {
          setShowSeachLocation(!showSeachLocation);
        }}
        style={[
          styles.modalContainer,
          { width: screenWidth, backgroundColor: colors.transparent },
        ]}
        backdropOpacity={0.4}>
        <TouchableWithoutFeedback
          onPress={() => {
            setShowSeachLocation(!showSeachLocation);
          }}>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{
              width: screenWidth * 0.9,
              justifyContent: 'center',
              alignSelf: 'center',
              flex: 1,
            }}>
            <View
              style={[
                styles.modalInnerContainer,
                { backgroundColor: colors.alimLightBlue },
              ]}
            >
              <Text style={[styles.headerText, { color: colors.primaryWhite }]}>
                Select location
              </Text>
              <Text
                style={[styles.locationSubText, { color: colors.primaryWhite }]}
              >
                View regional prayer times
              </Text>
              <View
                style={[styles.placeHolderContainer, { width: '100%' }]}>
                <GooglePlacesAutocomplete
                  ref={locationRef}
                  placeholder="Enter destination"
                  textInputProps={{
                    placeholderTextColor: colors.primaryWhite,
                    returnKeyType: 'search',
                    clearButtonMode: 'never',
                    onChangeText: text => {
                      if(!isConnected) {
                        Alert.alert(
                          'Warning',
                          'Offline : Turn on internet for changing location',
                          [
                            {
                              text: 'Ok',
                              onPress: () => {},
                              style: 'cancel',
                            },
                          ],
                        );
                      }
                    }
                  }}
                  enablePoweredByContainer={false}
                  predefinedPlaces={[]}
                  suppressDefaultStyles
                  keyboardShouldPersistTaps="handled"
                  onPress={async (data, details) => {           
                    const lat = details?.geometry?.location?.lat.toString();
                    const lng = details?.geometry.location.lng.toString();
                    const calculationMethod =
                      lat && lng && (await getCalculationMethod(lat, lng));                 
                    if (calculationMethod) {
                      const { calcMethod, timeZoneId } = calculationMethod;
                      const loc = {
                        location: data.description,
                        lat: details?.geometry.location.lat,
                        lng: details?.geometry.location.lng,
                      };
                      dispatch(saveLocation(loc));
                      dispatch(
                        storeCalculationSettings({
                          Method: calcMethod,
                          Timezone: timeZoneId,
                        }),
                      );
                      const historyLoc = {
                        location: data.description,
                        lat: details?.geometry.location.lat,
                        lng: details?.geometry.location.lng,
                        Method: calcMethod,
                        Timezone: timeZoneId,
                      };
                      dispatch(saveLocationHistory(historyLoc));
                    }
                    locationRef.current?.clear();
                    locationRef.current?.setAddressText('');
                    setShowSeachLocation(!showSeachLocation);
                  }}
                  query={{
                    key: Config.GOOGLE_MAPS_API_KEY,
                    language: 'en',
                  }}
                  fetchDetails={true}
                  minLength={2}
                  debounce={Platform.OS === 'android' ? 800 : 500}
                  timeout={20000}
                  styles={{
                    container: {
                      zIndex: 5,
                      width: screenWidth * 0.85,
                      borderRadius: 20,
                      backgroundColor: colors.alimDarkBg,
                    },
                    textInputContainer: {
                      paddingHorizontal: 40,
                      height: 50,
                      backgroundColor: colors.alimDarkBg,
                      borderRadius: 10,
                    },
                    textInput: {
                      backgroundColor: 'transparent',
                      fontSize: 16,
                      color: colors.primaryWhite,
                      flex: 1,
                    },
                    listView: {
                      position: 'absolute',
                      top: 60,
                      backgroundColor: 'white',
                      borderRadius: 5,
                      elevation: 3,
                      color: 'black',
                    },
                    description: {
                      color: 'black',
                      minHeight: 45,
                      paddingHorizontal: 10,
                      lineHeight: 45,
                    },
                    separator: {
                      height: 0.5,
                      backgroundColor: 'black',
                    },
                    predefinedPlacesDescription: {
                      color: '#1faadb',
                    },
                    poweredContainer: {
                      height: 45,
                      justifyContent: 'center',
                      alignItems: 'flex-end',
                      paddingEnd: 10,
                    },
                  }}
                  renderRightButton={() => (
                    <TouchableOpacity
                      onPress={() => {
                        locationRef.current?.clear();
                        locationRef.current?.blur();
                        locationRef.current?.setAddressText('');
                      }}
                      style={[styles.placeSearchTextinputicon, { right: 10 }]}
                    >
                      <Feather name='x' size={24} color={colors.primaryWhite} />
                    </TouchableOpacity>
                  )}
                  renderLeftButton={() => (
                    <TouchableOpacity
                      onPress={() => {}}
                      style={[styles.placeSearchTextinputicon, { left: 10 }]}>
                      <Feather
                        name="search"
                        size={24}
                        color={colors.primaryWhite}
                      />
                    </TouchableOpacity>
                  )}
                />
                <FlatList
                  bounces={false}
                  data={searchLocationHistory}
                  ListHeaderComponent={flatlistHeader}
                  style={[
                    styles.flatlistContainer,
                    {
                      backgroundColor: colors.alimDarkBg,
                    },
                  ]}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.flatlistInnerContainer}
                      onPress={() => {
                        dispatch(
                          saveLocation({
                            location: item.location,
                            lat: item.lat,
                            lng: item.lng,
                          }),
                        );
                        dispatch(
                          storeCalculationSettings({
                            Method: item.Method,
                            Timezone: item.Timezone,
                          }),
                        );
                        setShowSeachLocation(!showSeachLocation);
                      }}>
                      <View style={styles.popUpTitlecontainer}>
                        <View style={styles.locationIconContainer}>
                          <Image
                            source={require('../assets/images/_location.png')}
                            style={[styles.locateMeImage, styles.alignCenter]}
                            tintColor={colors.primaryWhite}
                          />
                        </View>
                        <Text
                          style={[
                            styles.locationHeaderText,
                            styles.alignCenter,
                            { color: colors.primaryWhite, width: '75%' },
                          ]}
                        >
                          {item.location}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </TouchableWithoutFeedback>
      </Modal>
      {isAboutVisible && <About />}
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.alimDarkBg,
  },
  prayerType: {
    flexDirection: 'row',
    justifyContent: 'center',
    height: 20,
    marginVertical: 11,
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  prayerTitle: {
    height: 30,
    justifyContent: 'center',
    flex: 1,
  },
  prayerTitleText: {
    color: colors.primaryWhite,
    fontFamily: fontFamily.regular,
    fontWeight: '600',
    fontSize: fontSize.font14,
  },
  titleText: {
    color: colors.primaryWhite,
    fontFamily: fontFamily.regular,
    fontWeight: '600',
    fontSize: fontSize.font18,
  },
  prayerTimeView: {
    height: 30,
    justifyContent: 'center',
  },
  prayerTimeText: {
    color: colors.primaryWhite,
    textAlign: 'left',
    fontFamily: fontFamily.regular,
    fontWeight: '600',
    fontSize: fontSize.font15,
    lineHeight: 26,
  },
  headerStyle: {
    fontSize: fontSize.font20,
    color: colors.primaryWhite,
    marginTop: 10,
    marginHorizontal: 15,
    fontFamily: fontFamily.bold,
    fontWeight: '600',
    height: Platform.OS === 'ios' && Platform.isPad ? 70 : 35,
  },
  date: {
    color: colors.primaryWhite,
    fontSize: fontSize.font15,
    fontWeight: '600',
    lineHeight: 24,
    textAlign: 'center',
  },
  arabicDate: {
    color: colors.primaryWhite,
    fontSize: fontSize.font10,
    fontWeight: '600',
    lineHeight: 16,
    textAlign: 'center',
  },
  dateContainer: {
    alignSelf: 'center',
    marginVertical: 15,
    flexDirection: 'row',
    height: 50,
    borderRadius: 10,
    backgroundColor: colors.alimButtonRed,
  },
  dateSubContainer: {
    flex: 1,
    alignItems: 'center',
    alignSelf: 'center',
    padding: 10,
    justifyContent: 'center',
  },
  icon: {
    alignSelf: 'center',
    padding: 5,
  },
  header: {
    width: Platform.OS === 'ios' && Platform.isPad ? '60%' : '50%',
    justifyContent: 'center',
  },
  headerContainer: {
    height: hp(5),
    flexDirection: 'row',
  },
  headerText: {
    fontSize: fontSize.font18,
    fontWeight: '600',
    color: colors.primaryWhite,
    justifyContent: 'center',
    padding: 4,
  },
  separator: {
    height: 0.5,
    alignSelf: 'center',
    backgroundColor: colors.grey,
  },
  flatlistBox: {
    alignSelf: 'center',
    justifyContent: 'center',
    backgroundColor: colors.alimLightBlue,
    borderRadius: 10,
    alignContent: 'center',
    width: '90%',
    marginBottom: 10,
  },
  popUp: {
    backgroundColor: colors.alimDarkBg,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    padding: 30,
  },
  popUpTitlecontainer: {
    flexDirection: 'row',
  },
  popUpContentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  minSelectorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  remindMeButton: {
    height: 45,
    width: 200,
    marginTop: 17,
    backgroundColor: colors.alimButtonRed,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  remindMeButtontext: {
    fontSize: 17,
    color: colors.primaryWhite,
  },
  appBar: {
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    overflow: 'hidden',
    alignItems: 'center',
    paddingTop: 10,
  },
  appbarInfoContainer: {
    justifyContent: 'center',
  },
  currentPrayerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  locationContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  currentPrayerName: {
    color: colors.primaryWhite,
    fontSize: 25,
    fontWeight: 'bold',
  },
  remainingTime: {
    color: colors.primaryWhite,
    fontSize: 18,
    fontWeight: '100',
  },
  appBarBackgroundImage: {
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  locationStyle: {
    color: colors.primaryWhite,
    fontSize: 18,
    fontWeight: '400',
  },
  image: {
    width: 18,
    height: 18,
    marginRight: 10,
  },
  largeImage: {
    width: 33,
    height: 33,
    marginRight: 10,
    alignSelf: 'center',
  },
  remindText: {
    fontSize: 7,
    fontWeight: '600',
    lineHeight: 8,
  },
  timeTitle: {
    justifyContent: 'center',
  },
  placeSearchTextinputicon: {
    position: 'absolute',
    height: 50,
    justifyContent: 'center',
    zIndex: 12,
  },
  locateMeImage: {
    height: 24,
    width: 24,
    justifyContent: 'center',
  },
  locateMeText: {
    color: colors.alimButtonRed,
    alignSelf: 'center',
    fontSize: 12,
    paddingLeft: 5,
  },
  locationIconContainer: {
    width: 39,
    height: 39,
    justifyContent: 'center',
    marginLeft: 10,
  },
  locationSubText: {
    fontSize: fontSize.font13,
    color: colors.primaryWhite,
    fontWeight: '300',
    justifyContent: 'center',
    paddingBottom: 8,
  },
  modalContainer: {
    borderRadius: 20,
    alignSelf: 'center',
    padding: 16,
  },
  modalInnerContainer: {
    padding: 16,
    borderRadius: 20,
  },
  placeHolderContainer: {
    position: 'relative',
    alignSelf: 'center',
    zIndex: 1,
  },
  flatlistContainer: {
    borderRadius: 20,
    backgroundColor: colors.red,
    marginTop: 10,
  },
  flatlistInnerContainer: {
    height: 60,
    justifyContent: 'center',
  },
  alignCenter: {
    alignSelf: 'center',
  },
  locationHeaderText: {
    fontSize: fontSize.font18,
    fontWeight: '600',
    color: colors.primaryWhite,
    justifyContent: 'center',
    paddingLeft: 5,
  },
  renderContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  flatlistSubContainer: {
    height: 100,
    borderRadius: 10,
    // marginHorizontal: 20,
  },
});

export default Prayer;
