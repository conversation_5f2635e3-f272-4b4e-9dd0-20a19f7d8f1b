import {
  Alert,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {ScrollView} from 'react-native-gesture-handler';
import {prayerData} from '../data/prayerData';
import colors from '../constants/colors';
import CustomSettingsSwitch from '../components/CustomSettingsSwitch';
import CustomModal from '../components/CustomModal';
import {storeCalculationSettings} from '../redux/slices/prayerSlice';
import {useAppDispatch, useAppSelector} from '../redux';
import {getCalculationMethod} from '../utils/Prayerutils';
import {RootStackScreenProps} from '../types/types';

interface ItemComponentProps {
  item: {label: string; value: string};
  isSelected: boolean;
  handleSelection: (val: string) => void;
}

const PrayerSettings: React.FC<RootStackScreenProps<'prayerSettings'>> = ({
  navigation,
}) => {
  const dispatch = useAppDispatch();
  const {juristicsList, methodsList, highlatsList} = prayerData;
  const {calculation} = useAppSelector(state => state.prayer.prayerSettings);
  const {Method, Highlats, Juristics, location} = calculation;
  const [selectedModal, setActiveModal] = useState('');
  const [selectedMethod, setMethod] = useState(Method);
  const [selectedJuristics, setJuristcisVal] = useState(Juristics);
  const [selectedHighlats, setHighlats] = useState(Highlats);
  const [isReset, setIsReset] = useState(false);

  const resetConfirmation = () => {
    setIsReset(true);
    Alert.alert(
      'Reset to Defaults',
      'Are you sure you want to reset to defaults?',
      [
        {text: 'Cancel', onPress: cancelResetOption},
        {text: 'Reset', onPress: resetToDefaults},
      ],
    );
  };

  const cancelResetOption = () => {
    setIsReset(false);
  };

  const resetToDefaults = async () => {
    setIsReset(false);
    const {lat, lng} = calculation;
    const result = await getCalculationMethod(lat, lng);
    dispatch(
      storeCalculationSettings({
        Method: result ? result?.calcMethod : 'ISNA',
        Juristics: 'Shafii (Standard)',
        Highlats: 'Angle /60th of Night',
        Timezone: result?.timeZoneId,
      }),
    );
    navigation.goBack();
  };

  const onSave = async () => {
    const newCalc = {
      Method: selectedMethod,
      Juristics: selectedJuristics,
      Highlats: selectedHighlats,
    };

    dispatch(storeCalculationSettings(newCalc));
    navigation.goBack();
  };

  const toggleModal = (modalId: string) => {
    setActiveModal(modalId);
  };

  const handleMethodSelection = (value: string) => {
    setMethod(value);
  };

  const handleJuristicsSelection = (value: string) => {
    setJuristcisVal(value);
  };

  const handleHighlatsSelection = (value: string) => {
    setHighlats(value);
  };

  return (
    <View style={[styles.bgView, {backgroundColor: colors.alimDarkBg}]}>
      <ScrollView style={styles.container}>
        <View style={styles.subView}>
          <View style={styles.locationView}>
            <Text style={[styles.content, {color: colors.primaryWhite}]}>
              Location
            </Text>
            <Text style={[styles.rightContent, {color: colors.primaryWhite}]}>
              {location}
            </Text>
          </View>
        </View>
        <View style={styles.lineView} />
        <View style={styles.subView}>
          <TouchableOpacity
            style={styles.alignCenter}
            onPress={() => toggleModal('method')}>
            <View style={styles.alignCenter}>
              <View style={styles.contentView}>
                <Text style={[styles.content, {color: colors.primaryWhite}]}>
                  Method
                </Text>
                <Text
                  style={[styles.rightContent, {color: colors.primaryWhite}]}>
                  {selectedMethod}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
          <CustomModal
            isVisible={selectedModal === 'method'}
            onSubmit={() => toggleModal('')}
            onReset={() => {
              handleMethodSelection(Method);
              toggleModal('');
            }}>
            <View style={{top: 10}}>
              {methodsList.map(item => (
                <ModalItemComponent
                  key={item.label}
                  item={item}
                  isSelected={item.label === selectedMethod}
                  handleSelection={() => handleMethodSelection(item.label)}
                />
              ))}
            </View>
          </CustomModal>
        </View>
        <View style={styles.lineView} />
        <View style={styles.subView}>
          <TouchableOpacity
            style={styles.alignCenter}
            onPress={() => toggleModal('juristics')}>
            <View style={styles.alignCenter}>
              <View style={styles.contentView}>
                <Text style={[styles.content, {color: colors.primaryWhite}]}>
                  Juristics
                </Text>
                <Text
                  style={[styles.rightContent, {color: colors.primaryWhite}]}>
                  {selectedJuristics}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
          <CustomModal
            isVisible={selectedModal === 'juristics'}
            onSubmit={() => toggleModal('')}
            onReset={() => {
              handleJuristicsSelection(Juristics);
              toggleModal('');
            }}>
            <View style={{top: 10}}>
              {juristicsList.map(item => (
                <ModalItemComponent
                  key={item.label}
                  item={item}
                  isSelected={item.label === selectedJuristics}
                  handleSelection={() => handleJuristicsSelection(item.label)}
                />
              ))}
            </View>
          </CustomModal>
        </View>
        <View style={styles.lineView} />
        <View style={styles.subView}>
          <TouchableOpacity
            style={styles.alignCenter}
            onPress={() => toggleModal('highlats')}>
            <View style={styles.alignCenter}>
              <View style={styles.contentView}>
                <Text style={[styles.content, {color: colors.primaryWhite}]}>
                  Highlats
                </Text>
                <Text
                  style={[styles.rightContent, {color: colors.primaryWhite}]}>
                  {selectedHighlats}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
          <CustomModal
            isVisible={selectedModal === 'highlats'}
            onSubmit={() => toggleModal('')}
            onReset={() => {
              handleHighlatsSelection(Highlats);
              toggleModal('');
            }}>
            <View style={{top: 10}}>
              {highlatsList.map(item => (
                <ModalItemComponent
                  key={item.label}
                  item={item}
                  isSelected={item.label === selectedHighlats}
                  handleSelection={() => handleHighlatsSelection(item.label)}
                />
              ))}
            </View>
          </CustomModal>
        </View>
        <View style={styles.lineView} />
        <View style={styles.switchContainerRow}>
          <Text style={[styles.content, {color: colors.primaryWhite}]}>
            Reset to Defaults
          </Text>
          <CustomSettingsSwitch
            isEnabled={isReset}
            onValueChange={resetConfirmation}
          />
        </View>
        <View style={styles.lineView} />
      </ScrollView>
      <View style={styles.buttonView}>
        <TouchableOpacity onPress={() => onSave()} style={styles.saveBtnView}>
          <Text style={[styles.saveBtnText, {color: colors.primaryWhite}]}>
            Save
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const ModalItemComponent = ({
  item,
  isSelected,
  handleSelection,
}: ItemComponentProps) => {
  return (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => handleSelection(item.label)}>
      <Text style={[styles.text, {color: colors.primaryWhite}]}>
        {item.label}
      </Text>
      {isSelected && (
        <Image
          source={require('../assets/images/checkSelected.png')}
          style={styles.checkboxImage}
        />
      )}
    </TouchableOpacity>
  );
};

export default PrayerSettings;

const styles = StyleSheet.create({
  bgView: {
    flex: 1,
  },

  container: {
    flexDirection: 'column',
    flex: 1,
  },
  lineView: {
    height: 1,
    backgroundColor: colors.alimLightBlue,
  },
  subView: {
    height: 56,
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  contentView: {
    flexDirection: 'row',
  },
  alignCenter: {
    justifyContent: 'center',
  },
  locationView: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'space-between',
  },
  content: {
    width: '50%',
  },
  rightContent: {
    width: '50%',
    textAlign: 'right',
  },
  switchContainerRow: {
    flexDirection: 'row',
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  modalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
  },
  text: {
    fontSize: 16,
  },
  checkboxImage: {
    height: 20,
    width: 20,
  },
  buttonView: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  saveBtnView: {
    width: '90%',
    height: 56,
    borderRadius: 100,
    justifyContent: 'center',
    backgroundColor: colors.alimLightBlue,
  },
  saveBtnText: {
    color: colors.primaryWhite,
    fontWeight: '600',
    fontSize: 16,
    alignSelf: 'center',
  },
});
