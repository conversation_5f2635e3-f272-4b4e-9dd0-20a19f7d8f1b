import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import Modal from 'react-native-modal/dist/modal';
import colors from '../constants/colors';
import { setAboutVisibility } from '../redux/slices/prayerSlice';
import { useAppDispatch, useAppSelector } from '../redux';

const font = Platform.OS === 'ios' ? 'Quicksand-Regular' : 'QuicksandRegular';

const About = () => {
  const appVersion = DeviceInfo.getVersion();
  const isAboutVisible = useAppSelector((state) => state.prayer.isAboutVisible);
  const dispatch = useAppDispatch();

  return (
    <Modal
      isVisible={isAboutVisible}
      onBackdropPress={() => dispatch(setAboutVisibility(false))}
      backdropOpacity={0}
      animationIn={'slideInRight'}
      style={styles.popupContainer}
    >
      <View style={styles.popUpView}>
        <View style={styles.topBar}>
          <Text style={styles.heading}>About</Text>
        </View>
        <Text style={styles.subTitle}>Alim Prayer Minder</Text>
        <Text style={styles.detail}>{`Version: ${appVersion}`}</Text>
        <Text style={styles.detail}>© 2025 Alim Foundation Inc</Text>
        <Text style={styles.detail}>www.alim.org</Text>
        <View style={styles.bottomBar}>
          <TouchableOpacity
            onPress={() => dispatch(setAboutVisibility(false))}
            style={styles.buttonView}
          >
            <Text style={styles.heading}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  popupContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  popUpView: {
    width: 250,
    height: 220,
    backgroundColor: colors.primaryWhite,
  },
  heading: {
    textAlign: 'center',
    color: colors.primaryWhite,
    lineHeight: 45,
    fontFamily: font,
    fontSize: 16,
    fontWeight: '600',
  },
  subTitle: {
    color: colors.alimDarkBg,
    textAlign: 'center',
    lineHeight: 45,
    fontFamily: font,
  },
  detail: {
    color: colors.alimDarkBg,
    textAlign: 'center',
    fontFamily: font,
  },
  topBar: {
    backgroundColor: colors.alimLightBlue,
    width: 250,
    height: 50,
    justifyContent: 'center',
  },
  bottomBar: {
    width: 250,
    height: 50,
    position: 'absolute',
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonView: {
    backgroundColor: colors.alimLightBlue,
    width: 100,
    borderRadius: 10,
    justifyContent: 'center',
  },
});

export default About;
