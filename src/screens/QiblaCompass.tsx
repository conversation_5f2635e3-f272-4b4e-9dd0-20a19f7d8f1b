import Geolocation from '@react-native-community/geolocation';
import { Qibla } from 'qibla';
import { promptForEnableLocationIfNeeded } from 'react-native-android-location-enabler';
import CompassHeading from 'react-native-compass-heading';
import {
  Alert,
  Image,
  ImageBackground,
  Platform,
  StyleSheet,
  Text,
  View,
  Vibration,
  Modal,
  TouchableHighlight,
} from 'react-native';
import React, { useCallback, useState, useLayoutEffect } from 'react';
import {
  PERMISSIONS,
  RESULTS,
  check,
  openSettings,
  request,
} from 'react-native-permissions';
import {
  Circle,
  Defs,
  LinearGradient,
  Path,
  Stop,
  Svg,
} from 'react-native-svg';
import { useHeaderHeight } from '@react-navigation/elements';
import Feather from 'react-native-vector-icons/Feather';
import { useRoute } from '@react-navigation/native';

import DeviceInfo from 'react-native-device-info';
import { magnetometer } from 'react-native-sensors';
import armoment from 'moment-hijri';
import { useAppSelector } from '../redux';
import { RootStackScreenProps } from '../types/types';
import { Constants, width, height, hp } from '../constants/constants';
import colors from '../constants/colors';
import 'moment/locale/ar';
import { fontSize } from '../utils/fontUtils';

const QiblaCompass: React.FC<RootStackScreenProps<'qiblaCompass'>> = ({
  navigation,
}) => {
  
  const headerLeft = () => (
    <Feather
      name='chevron-left'
      size={hp(3.5)}
      color={colors.primaryWhite}
      onPress={() => {
        navigation.goBack();
      }}
    />
  );

  useLayoutEffect(() => {
    navigation.setOptions({
      headerShown: true,
      headerLeft: () => headerLeft(),
      headerTitle: 'Qibla Finder',
      headerTitleStyle: {
        color: colors.primaryWhite,
        fontSize: fontSize.font16,
        fontWeight: '500',
      },
      headerStyle: { backgroundColor: colors.alimLightBlue },
    });
  }, [navigation]);

  const headerHeight = useHeaderHeight();
  const [compassHeading, setCompassHeading] = useState(0);
  const [qibilaInstruction, setqibilaInstruction] = useState('');
  const [qiblaFromTrueNorth, setqiblaFromTrueNorth] = useState(0);
  const [angleDifferenceValue, setDifferenceValue] = useState(0);
  const { isConnected } = useAppSelector((state) => state.internet);
  const [magnetometerData, setMagnetometerData] = useState(1);
  const [showMageneticModal, setShowMageneticModal] = useState(false);
  armoment.locale('ar');
  const { calculation } = useAppSelector(
    (state) => state.prayer.prayerSettings
  );

  // Define constants for the arc
  const radius = 100;
  const cx = 150;
  const cy = 150;
  const startAngle = angleDifferenceValue > 180 ? 0 : 360;
  const endAngle = 360 - angleDifferenceValue;
  // Function to convert polar coordinates to cartesian coordinates
  const polarToCartesian = (
    cx: number,
    cy: number,
    r: number,
    angleInDegrees: number
  ) => {
    const angleInRadians = ((angleInDegrees - 90) * Math.PI) / 180.0;
    return {
      x: cx + r * Math.cos(angleInRadians),
      y: cy + r * Math.sin(angleInRadians),
    };
  };

  // Function to get the end x-coordinate of the arc
  const getEndX = (x: number, y: number, radius: number, endAngle: number) => {
    const end = polarToCartesian(x, y, radius, endAngle);
    return end.x;
  };

  // Function to get the end Y-coordinate of the arc
  const getEndY = (x: number, y: number, radius: number, endAngle: number) => {
    const end = polarToCartesian(x, y, radius, endAngle);
    return end.y;
  };

  // Function to get the start X-coordinate of the arc
  const describeArc = (
    x: number,
    y: number,
    radius: number,
    startAngle: number,
    endAngle: number
  ) => {
    const start = polarToCartesian(x, y, radius, startAngle);
    const end = polarToCartesian(x, y, radius, endAngle);
    const largeArcFlag = Math.abs(endAngle - startAngle) <= 180 ? '0' : '1';
    // Sweep flag to determine direction of arc (clockwise or counterclockwise)
    const sweepFlag = endAngle > 180 ? '0' : '1';
    const d = [
      'M',
      start.x,
      start.y,
      'A',
      radius,
      radius,
      0,
      largeArcFlag,
      sweepFlag,
      end.x,
      end.y,
    ].join(' ');
    return d;
  };

  React.useEffect(() => {
    if (Platform.OS === 'android') {
      const subscription = magnetometer.subscribe(({ x, y, z }) => {
        const totalMagneticField = Math.sqrt(x ** 2 + y ** 2 + z ** 2);
        setMagnetometerData(totalMagneticField);
        if (totalMagneticField > 60) {
          setShowMageneticModal(true);
        }
      });
      return () => subscription.unsubscribe();
    }
  }, [magnetometerData, navigation]);

  const getCurrentLocation = useCallback(() => {
    Geolocation.getCurrentPosition((position) => {
      const qiblaFromNorth = Qibla.degreesFromTrueNorth(
        Number(position.coords.latitude),
        Number(position.coords.longitude)
      );
      setqiblaFromTrueNorth(qiblaFromNorth);
    });
  }, []);

  const showLocationEnableAlert = () => {
    Alert.alert(
      'Enable location access',
      `To continue using this service, we require your permission to access your device's location.`,
      [
        {
          text: 'Go to settings',
          onPress: () => {
            openSettings();
          },
          style: 'cancel',
        },
        {
          text: 'Cancel',
          onPress: () => {},
          style: 'cancel',
        },
      ]
    );
  };

  const checkInternetConnectivity = useCallback(() => {
    if (isConnected) {
      requestLocationPermission();
    } else {
      Alert.alert(
        'No internet connection',
        'It appears that there is no internet connection. Please check your internet connection and try again.',
        [
          {
            text: 'Ok',
            onPress: () => {
              navigation.goBack();
            },
            style: 'cancel',
          },
        ]
      );
    }
  }, [isConnected, navigation, requestLocationPermission]);

  const checkLocationServices: () => Promise<void> = useCallback(async () => {
    try {
      const enableResult = await promptForEnableLocationIfNeeded();
      if (enableResult === 'enabled') {
        checkInternetConnectivity();
      } else {
        navigation.goBack();
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error(error.message);
      }
    }
  }, [checkInternetConnectivity, navigation]);

  const requestLocationPermission = useCallback(async () => {
    try {
      const isLocationEnabled = await DeviceInfo.isLocationEnabled();

      if (!isLocationEnabled && calculation.location.includes('Default')) {
        if (Platform.OS === 'android') {
          checkLocationServices();
        } else {
          Alert.alert('', 'To continue, turn on your device location.', [
            {
              text: 'Ok',
              onPress: () => {
                navigation.goBack();
              },
              style: 'cancel',
            },
          ]);
        }
      } else if (!isConnected && calculation.location.includes('Default')) {
        Alert.alert(
          'No internet connection',
          'It appears that there is no internet connection. Please check your internet connection and try again.',
          [
            {
              text: 'Ok',
              onPress: () => {
                navigation.goBack();
              },
              style: 'cancel',
            },
          ]
        );
      } else {
        const permissionStatus = await check(
          Platform.select({
            android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
            ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
            default: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
          })
        );
        if (
          permissionStatus === RESULTS.DENIED ||
          permissionStatus === RESULTS.BLOCKED ||
          permissionStatus === RESULTS.UNAVAILABLE
        ) {
          const requestResult = await request(
            Platform.select({
              android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
              ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
              default: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
            })
          );
          if (requestResult === RESULTS.GRANTED  && !calculation.location.includes('Default')) {
            getCurrentLocation();
          } else {
            if(calculation.location.includes('Default')){
              navigation.goBack();
              showLocationEnableAlert();}
          }
        } else if (permissionStatus === RESULTS.GRANTED) {
          getCurrentLocation();
        } else {
            if(calculation.location.includes('Default')){
              navigation.goBack();
              showLocationEnableAlert();
          }
        }
      }
    } catch (error) {
      console.error('Error checking/requesting location permission:', error);
    }
  }, [checkLocationServices, getCurrentLocation, isConnected, navigation]);

  React.useEffect(() => {
    requestLocationPermission();
  }, [getCurrentLocation, requestLocationPermission]);

  const normalizeAngle = (angle: number) => {
    return (angle + 360) % 360;
  };

  React.useEffect(() => {
    const degreeUpdateRate = 1;
    if (isConnected) {
      CompassHeading.start(
        degreeUpdateRate,
        ({ heading }: { heading: number }) => {
          setCompassHeading(heading);
          const angleDifference = normalizeAngle(heading - qiblaFromTrueNorth);
          setDifferenceValue(angleDifference);
          if (angleDifference <= 2 || angleDifference >= 358) {
            Vibration.vibrate(100);
            setqibilaInstruction('Pointing to Qibla.');
          } else if (angleDifference < 180) {
            setqibilaInstruction(' left.');
          } else {
            setqibilaInstruction(' right.');
          }
        }
      );
    }
    return () => {
      CompassHeading.stop();
    };
  }, [isConnected, qiblaFromTrueNorth]);

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.parentOverlay,

          {
            transform: [{ rotate: `${360 - endAngle}deg` }],
            top: 50,
          },
        ]}
      />
      <View style={styles.imageContainer}>
        <View
          style={[
            styles.kabaContainer,
            { top: (height - headerHeight) / 2 - 130 },
          ]}
        >
          <Image
            style={styles.kaba}
            alt='image'
            source={require('../assets/images/qibla-imgs/kaba.png')}
          />
        </View>

        <ImageBackground
          style={[
            styles.imgView,
            {
              transform: [{ rotate: `${360 - compassHeading}deg` }],
            },
          ]}
          resizeMode='contain'
          alt='image'
          source={require('../assets/images/qibla-imgs/compass-transparent.png')}
        >
          <Image
            tintColor={colors.alimButtonRed}
            style={[
              styles.needleStyle,

              { transform: [{ rotate: `${qiblaFromTrueNorth}deg` }] },
            ]}
            alt='image'
            source={require('../assets/images/qibla-imgs/compassNav.png')}
          />
        </ImageBackground>
      </View>
      <View
        style={{
          position: 'absolute',
          height: height - headerHeight,
          width,
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: -1,
        }}
      >
        <View style={[styles.arcContainer, {}]}>
          <Svg height='300' width='300'>
            <Defs>
              <LinearGradient id='grad' x1='0%' y1='0%' x2='0%' y2='100%'>
                <Stop offset='0%' stopColor='white' stopOpacity='0.2' />
                <Stop offset='100%' stopColor='white' stopOpacity='1' />
              </LinearGradient>
            </Defs>
            <Path
              d={describeArc(cx, cy, radius, startAngle, endAngle)}
              fill='none'
              stroke='url(#grad)'
              strokeWidth='6'
            />
            {!(angleDifferenceValue <= 8 || angleDifferenceValue >= 352) && (
              <Circle
                cx={getEndX(cx, cy, radius, endAngle)}
                cy={getEndY(cx, cy, radius, endAngle)}
                r={7}
                fill='white'
              />
            )}
          </Svg>
        </View>
      </View>

      <View style={{ alignItems: 'center' }} />
      {showMageneticModal && (
        <Modal visible={true}>
          <View
            style={[
              styles.modalContainer,
              { backgroundColor: colors.alimDarkBg, flex: 1 },
            ]}
          >
            <View style={{ paddingVertical: 10 }}>
              <Text
                style={{
                  color: colors.primaryWhite,
                  fontSize: fontSize.font20,
                }}
              >
                Magnetic Field Detected
              </Text>
            </View>
            <View style={styles.magneticModalContainer}>
              <View style={styles.modalContainer}>
                <Image
                  style={{ width: 200, height: 200 }}
                  source={require('../assets/images/caliberate.jpg')}
                />
                <Text
                  style={{
                    color: colors.primaryWhite,
                    fontSize: 18,
                    textAlignVertical: 'center',
                  }}
                >
                  {Constants.qiblaMageneticDetectText}
                </Text>
              </View>
            </View>
            <TouchableHighlight
              style={{
                paddingVertical: 10,
              }}
              underlayColor={'transparent'}
              onPress={() => {
                setShowMageneticModal(!showMageneticModal);
              }}
            >
              <Text
                style={{
                  color: colors.primaryWhite,
                  fontSize: fontSize.font20,
                }}
              >
                CHECK NOW
              </Text>
            </TouchableHighlight>
          </View>
        </Modal>
      )}

      <View style={styles.bottomcontainer}>
        <View
          style={[
            styles.instructionContainer,
            {
              backgroundColor:
                qibilaInstruction === 'Pointing to Qibla.'
                  ? colors.alimButtonRed
                  : colors.alimDarkBg,
            },
          ]}
        >
          {qibilaInstruction === 'Pointing to Qibla.' ? (
            <Text style={styles.instructionTypeStyle}>{qibilaInstruction}</Text>
          ) : (
            <>
              <Text style={styles.instructionStyle}>Turn to your</Text>
              <Text style={styles.instructionTypeStyle1}>
                {qibilaInstruction}
              </Text>
            </>
          )}
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backIconBgColor,
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  magneticModalContainer: {
    backgroundColor: colors.magneticColor,
    paddingVertical: 10,
    paddingHorizontal: 30,
  },
  instructionStyle: {
    fontSize: 18,
    fontWeight: '200',
    lineHeight: 21,
    color: colors.primaryWhite,
  },
  instructionTypeStyle: {
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 21,
    color: colors.lightWhiteColor,
  },
  instructionTypeStyle1: {
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 21,
    color: colors.primaryWhite,
  },
  needleStyle: {
    width: 70,
    height: 70,
  },
  imgView: {
    width: 350,
    height: 350,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  kabaContainer: {
    height: 60,
    width,
    alignContent: 'center',
    justifyContent: 'center',
    position: 'absolute',
  },
  bottomcontainer: {
    width,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    bottom: 50,
    left: 0,
  },
  kaba: {
    height: 40,
    width: 40,
    alignSelf: 'center',
  },
  instructionContainer: {
    backgroundColor: colors.alimLightBlue,
    height: 75,
    width: width - 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    flexDirection: 'row',
  },
  arcContainer: {
    position: 'absolute',
    left: (width - 300) / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  parentOverlay: {
    height: height * 3.5,
    width,
    position: 'absolute',
    zIndex: 1000,
    opacity: 0.3,
    marginTop: 50,
  },
});
export default QiblaCompass;
