import {View, TouchableOpacity, StyleSheet} from 'react-native';
import colors from '../constants/colors';

type CustomSwitchProps = {
  isEnabled: boolean;
  onValueChange: () => void;
};

const CustomSettingsSwitch = ({
  isEnabled,
  onValueChange,
}: CustomSwitchProps) => {
  return (
    <TouchableOpacity
      style={[
        styles.switchContainer,
        isEnabled ? styles.enabledBackground : styles.disabledBackground,
      ]}
      onPress={onValueChange}
      activeOpacity={0.8}>
      <View style={styles.switchCircle} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  switchContainer: {
    width: 50,
    height: 30,
    borderRadius: 15,
    padding: 3,
    flexDirection: 'row',
    alignItems: 'center',
  },
  enabledBackground: {
    backgroundColor: colors.alimButtonRed,
    justifyContent: 'flex-end',
  },
  disabledBackground: {
    backgroundColor: colors.alimLightBlue,
    justifyContent: 'flex-start',
  },

  switchCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primaryWhite,
  },
});

export default CustomSettingsSwitch;
