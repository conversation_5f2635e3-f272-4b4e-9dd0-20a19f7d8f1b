import React from 'react';
import {
  Image,
  ImageProps,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import colors from '../constants/colors';
import {fontFamily, fontSize} from '../utils/fontUtils';
import {hp} from '../constants/constants';

interface IProps {
  titleText: string;
  descriptionText: string;
  footerText: string;
  backgroundColor: string;
  imageSource: ImageProps;
  onItemPress?: () => void;
}

const WidgetComponent: React.FC<IProps> = ({
  titleText,
  descriptionText,
  footerText,
  backgroundColor,
  imageSource,
  onItemPress,
}) => {
  return (
    <View>
      <TouchableOpacity
        style={[styles.widgetView, {backgroundColor}]}
        onPress={onItemPress}>
        <View style={styles.textContainer}>
          <Text style={styles.titleText}>{titleText}</Text>
          <Text style={styles.descriptionText}>{descriptionText}</Text>
          <Text style={styles.bottomText}>{footerText}</Text>
        </View>
        <View style={styles.imageContainer}>
          <Image
            source={imageSource}
            style={styles.imageInsideWidget}
            resizeMode="stretch"
          />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default WidgetComponent;

const styles = StyleSheet.create({
  widgetView: {
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: hp(16),
    width: '90%',
    borderRadius: 25,
    marginVertical: 15,
    overflow: 'hidden',
  },
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 10,
    zIndex: 1,
  },
  textContainer: {
    justifyContent: 'space-between',
    marginRight: 10,
    width: '55%',
  },
  titleText: {
    color: colors.primaryWhite,
    fontSize: fontSize.font13,
    fontWeight: '400',
    marginLeft: 15,
    marginBottom: 5,
    fontFamily: fontFamily.regular,
  },
  descriptionText: {
    color: colors.primaryWhite,
    fontSize: fontSize.font19,
    fontWeight: '700',
    marginLeft: 15,
    marginBottom: 5,
    flexWrap: 'wrap',
    fontFamily: fontFamily.bold,
  },
  bottomText: {
    color: colors.primaryWhite,
    fontSize: fontSize.font16,
    fontWeight: '600',
    marginLeft: 15,
    fontFamily: fontFamily.bold,
  },
  imageInsideWidget: {
    height: '100%',
    resizeMode: 'contain',
    width: '100%',
  },
  imageContainer: {
    height: '100%',
    width: '45%',
    overflow: 'hidden',
    alignItems: 'flex-end',
  },
});
