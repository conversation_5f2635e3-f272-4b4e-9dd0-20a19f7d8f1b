import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  useImperativeHandle,
  ReactNode,
  Ref,
} from 'react';
import {
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  StyleSheet,
  Text,
  View,
  ViewProps,
  ViewStyle,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
// import { trigger } from 'react-native-haptic-feedback';
import colors from '../constants/colors';

function isNumeric(str: string | unknown): boolean {
  if (typeof str === 'number') return true;
  if (typeof str !== 'string') return false;
  return !isNaN(str as unknown as number) && !isNaN(parseFloat(str));
}

const deviceWidth = Dimensions.get('window').width;

const isViewStyle = (style: ViewProps['style']): style is ViewStyle => {
  return (
    typeof style === 'object' &&
    style !== null &&
    Object.keys(style).includes('height')
  );
};

export type PickerProps<ItemT extends string | number> = {
  style?: ViewProps['style'];
  dataSource: Array<ItemT>;
  selectedIndex?: number;
  onValueChange?: (value: ItemT, index: number) => void;
  renderItem?: (data: ItemT, index: number, isSelected: boolean) => JSX.Element;
  highlightColor?: string;
  highlightBorderWidth?: number;
  itemTextStyle?: object;
  activeItemTextStyle?: object;
  itemHeight?: number;
  wrapperHeight?: number;
  wrapperBackground?: string;
  scrollViewComponent?: any;
  title?: string;
  userWidth?: number;
  userBackgroundColor?: string;
  titleColor?: string;
};

export type PickerHandle = {
  scrollToTargetIndex: (val: number) => void;
};

const Picker: {
  <ItemT extends string | number>(
    props: PickerProps<ItemT> & {ref?: Ref<PickerHandle>},
  ): ReactNode;
} = React.forwardRef((propsState, ref) => {
  const {style, scrollViewComponent, userWidth, userBackgroundColor, ...props} =
    propsState;
  const itemHeight = 30;
  const [initialized, setInitialized] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(
    props.selectedIndex && props.selectedIndex >= 0 ? props.selectedIndex : 0,
  );
  const sView = useRef<ScrollView>(null);
  const [isScrollTo, setIsScrollTo] = useState(false);
  const [dragStarted, setDragStarted] = useState(false);
  const [momentumStarted, setMomentumStarted] = useState(false);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);
  const lastScrollY = useRef(0);

  useImperativeHandle(ref, () => ({
    scrollToTargetIndex: (val: number) => {
      setSelectedIndex(val);
      sView?.current?.scrollTo({y: val * itemHeight});
    },
  }));

  const wrapperHeight =
    props.wrapperHeight ||
    (isViewStyle(style) && isNumeric(style.height)
      ? Number(style.height)
      : 0) ||
    itemHeight * 5;

  useEffect(
    function initialize() {
      if (initialized) return;
      setInitialized(true);

      setTimeout(() => {
        const y = itemHeight * selectedIndex;
        sView?.current?.scrollTo({y});
      }, 0);

      return () => {
        timer && clearTimeout(timer);
      };
    },
    [initialized, itemHeight, selectedIndex, sView, timer],
  );

  const renderPlaceHolder = () => {
    const h = (wrapperHeight - itemHeight) / 2;
    const header = <View style={{height: h, flex: 1}} />;
    const footer = <View style={{height: h, flex: 1}} />;
    return {header, footer};
  };

  const renderItem = (data: (typeof props.dataSource)[0], index: number) => {
    const isSelected = index === selectedIndex;
    const item = props.renderItem ? (
      props.renderItem(data, index, isSelected)
    ) : (
      <Text
        style={
          isSelected
            ? [
                props.activeItemTextStyle
                  ? props.activeItemTextStyle
                  : styles.activeItemTextStyle,
              ]
            : [props.itemTextStyle ? props.itemTextStyle : styles.itemTextStyle]
        }>
        {data}
      </Text>
    );

    return (
      <View style={[styles.itemWrapper, {height: itemHeight}]} key={index}>
        {item}
      </View>
    );
  };
  const scrollFix = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      let y = 0;
      const h = itemHeight;
      if (e.nativeEvent.contentOffset) {
        y = e.nativeEvent.contentOffset.y;
      }
      const _selectedIndex = Math.round(y / h);

      const _y = _selectedIndex * h;
      if (_y !== y) {
        if (Platform.OS === 'ios') {
          setIsScrollTo(true);
        }
        sView?.current?.scrollTo({y: _y});
      }
      if (selectedIndex === _selectedIndex) {
        return;
      }
      if (props.onValueChange) {
        const selectedValue = props.dataSource[_selectedIndex];
        setSelectedIndex(_selectedIndex);
        props.onValueChange(selectedValue, _selectedIndex);
      }
    },
    [itemHeight, props, selectedIndex],
  );

  const onScrollBeginDrag = () => {
    setDragStarted(true);

    if (Platform.OS === 'ios') {
      setIsScrollTo(false);
    }
    timer && clearTimeout(timer);
  };

  const onScrollEndDrag = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    setDragStarted(false);
    const _e: NativeSyntheticEvent<NativeScrollEvent> = {...e};
    timer && clearTimeout(timer);
    setTimer(
      setTimeout(() => {
        if (!momentumStarted) {
          scrollFix(_e);
        }
      }, 50),
    );
  };
  const onMomentumScrollBegin = () => {
    setMomentumStarted(true);
    timer && clearTimeout(timer);
  };

  const onMomentumScrollEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    setMomentumStarted(false);

    if (!isScrollTo && !dragStarted) {
      scrollFix(e);
    }
  };

  const {header, footer} = renderPlaceHolder();
  const highlightWidth = (isViewStyle(style) ? style.width : 0) || deviceWidth;
  const wrapperStyle: ViewStyle = {
    height: 150,
    width: userWidth || Dimensions.get('screen').width / 5,
    marginRight: 2,
    marginTop: 40,
    backgroundColor: userBackgroundColor || colors.primaryBlack,
    overflow: 'hidden',
  };

  const highlightStyle: ViewStyle = {
    position: 'absolute',
    top: (wrapperHeight - itemHeight) / 2,
    height: itemHeight,
    width: highlightWidth,
    borderTopColor: colors.selectedItemColor,
    borderBottomColor: colors.selectedItemColor,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    backgroundColor: colors.selectedItemColor,
  };

  const CustomScrollViewComponent = scrollViewComponent || ScrollView;

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    if (Math.abs(currentScrollY - lastScrollY.current) > 20) {
      // trigger('impactLight', {
      //   enableVibrateFallback: true,
      //   ignoreAndroidSystemSettings: false,
      // });
      lastScrollY.current = currentScrollY;
    }
  };

  return (
    <View>
      {propsState?.title && (
        <Text
          style={[
            styles.title,
            {
              color: propsState?.titleColor
                ? propsState?.titleColor
                : colors.primaryWhite,
            },
          ]}>
          {propsState?.title}
        </Text>
      )}
      <View style={wrapperStyle}>
        <View style={highlightStyle} />
        <CustomScrollViewComponent
          ref={sView}
          bounces={false}
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled
          onMomentumScrollBegin={(_e: any) => onMomentumScrollBegin()}
          onMomentumScrollEnd={(e: NativeSyntheticEvent<NativeScrollEvent>) =>
            onMomentumScrollEnd(e)
          }
          onScroll={handleScroll}
          onScrollBeginDrag={(_e: any) => onScrollBeginDrag()}
          onScrollEndDrag={(e: NativeSyntheticEvent<NativeScrollEvent>) =>
            onScrollEndDrag(e)
          }
          scrollEventThrottle={16}>
          {header}
          {props.dataSource.map(renderItem)}
          {footer}
        </CustomScrollViewComponent>
      </View>
    </View>
  );
});
export default Picker;

const styles = StyleSheet.create({
  itemWrapper: {
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    color: colors.primaryWhite,
    alignSelf: 'center',
    top: 10,
    fontSize: 17,
  },
  itemTextStyle: {
    color: colors.primaryWhite,
  },
  activeItemTextStyle: {
    color: colors.grey,
  },
});
