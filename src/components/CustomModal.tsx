// CustomModal.tsx
import React, {ReactNode} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import colors from '../constants/colors';

interface CustomModalProps {
  isVisible: boolean;
  onSubmit: () => void;
  onReset: () => void;
  children: ReactNode;
}

const CustomModal: React.FC<CustomModalProps> = ({
  isVisible,
  onSubmit,
  onReset,
  children,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={() => onReset()}
      style={styles.modal}
      avoidKeyboard={true}
      propagateSwipe={true}
      backdropColor={colors.blurColor}>
      <View style={[styles.modalContent, {backgroundColor: colors.lightBlue}]}>
        {children}
        <TouchableOpacity
          onPress={onSubmit}
          style={[styles.doneBtnView, {bottom: insets.bottom}]}>
          <Text style={[styles.doneBtnText, {color: colors.primaryWhite}]}>
            Done
          </Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },

  modalContent: {
    backgroundColor: colors.alimLightBlue,
    paddingHorizontal: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    minHeight: 100,
  },
  doneBtnView: {
    width: '100%',
    height: 56,
    borderRadius: 100,
    justifyContent: 'center',
    marginTop: 50,
    backgroundColor: colors.alimDarkBg,
  },
  doneBtnText: {
    color: colors.primaryWhite,
    fontWeight: '600',
    fontSize: 16,
    alignSelf: 'center',
  },
});

export default CustomModal;
