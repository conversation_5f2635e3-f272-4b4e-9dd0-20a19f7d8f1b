import {CalculationParameters, Rounding} from 'adhan';

export const prayerData: {
  prayerList: [
    '<PERSON>ajr',
    // 'Sunrise',
    'Zuhr',
    '<PERSON>r',
    '<PERSON><PERSON>rib',
    '<PERSON><PERSON>',
  ];
  methodsList: {label: string; value: string}[];
  juristicsList: {label: string; value: string}[];
  highlatsList: {label: string; value: string}[];
  getCalculationParams: any;
} = {
  prayerList: [
    'Fajr',
    // 'Sunrise',
    'Zuhr',
    'Asr',
    'Maghrib',
    'Isha',
  ],
  methodsList: [
    {label: 'Karachi', value: '1'},
    {label: '<PERSON><PERSON><PERSON>', value: '2'},
    {label: 'ISNA', value: '3'},
    {label: 'MWL', value: '4'},
    {label: 'Makkah', value: '5'},
    {label: 'Egypt', value: '6'},
    {label: 'Tehran', value: '7'},
  ],
  juristicsList: [
    {label: '<PERSON><PERSON><PERSON><PERSON> (Standard)', value: '1'},
    {label: '<PERSON><PERSON><PERSON>', value: '2'},
  ],
  highlatsList: [
    {label: 'Angle /60th of Night', value: '1'},
    {label: 'No Adjustment', value: '2'},
    {label: 'Middle of Night', value: '3'},
    {label: '1/7th of Night', value: '3'},
  ],
  getCalculationParams: (methodName: string) => {
    switch (methodName) {
      case 'Egypt':
        return CalculationMethods.Egyptian();
      case 'Karachi':
        return CalculationMethods.Karachi();
      case 'MWL':
        return CalculationMethods.MuslimWorldLeague();
      case 'Tehran':
        return CalculationMethods.Tehran();
      case 'ISNA':
        return CalculationMethods.NorthAmerica();
      case 'Makkah':
        return CalculationMethods.UmmAlQura();
      case 'Jafari':
        return CalculationMethods.Jafari();
      default:
        return CalculationMethods.Karachi();
    }
  },
};

const CalculationMethods = {
  // Muslim World League
  MuslimWorldLeague() {
    const params = new CalculationParameters('MuslimWorldLeague', 18, 17);
    params.methodAdjustments.dhuhr = 1;
    return params;
  },

  // Egyptian General Authority of Survey
  Egyptian() {
    const params = new CalculationParameters('Egyptian', 19.5, 17.5);
    params.methodAdjustments.dhuhr = 1;
    return params;
  },

  // University of Islamic Sciences, Karachi
  Karachi() {
    const params = new CalculationParameters('Karachi', 18, 18);
    params.methodAdjustments.dhuhr = 1;
    return params;
  },

  // Umm al-Qura University, Makkah
  UmmAlQura() {
    return new CalculationParameters('UmmAlQura', 18.5, 0, 90);
  },

  // Dubai
  Dubai() {
    const params = new CalculationParameters('Dubai', 18.2, 18.2);
    params.methodAdjustments = {
      ...params.methodAdjustments,
      sunrise: -3,
      dhuhr: 3,
      asr: 3,
      maghrib: 3,
    };
    return params;
  },

  // Moonsighting Committee
  MoonsightingCommittee() {
    const params = new CalculationParameters('MoonsightingCommittee', 18, 18);
    params.methodAdjustments = {
      ...params.methodAdjustments,
      dhuhr: 5,
      maghrib: 3,
    };

    return params;
  },

  // ISNA
  NorthAmerica() {
    const params = new CalculationParameters('NorthAmerica', 15, 15);
    params.methodAdjustments.dhuhr = 1;
    return params;
  },

  // Kuwait
  Kuwait() {
    return new CalculationParameters('Kuwait', 18, 17.5);
  },

  // Qatar
  Qatar() {
    return new CalculationParameters('Qatar', 18, 0, 90);
  },

  // Singapore
  Singapore() {
    const params = new CalculationParameters('Singapore', 20, 18);
    params.methodAdjustments.dhuhr = 1;
    params.rounding = Rounding.Up;
    return params;
  },

  // Institute of Geophysics, University of Tehran
  Tehran() {
    const params = new CalculationParameters('Tehran', 17.7, 14, 0, 4.5);
    return params;
  },

  // Dianet
  Turkey() {
    const params = new CalculationParameters('Turkey', 18, 17);
    params.methodAdjustments = {
      ...params.methodAdjustments,
      sunrise: -7,
      dhuhr: 5,
      asr: 4,
      maghrib: 7,
    };
    return params;
  },

  // Other
  Jafari() {
    const params = new CalculationParameters('Other', 16, 14, 0, 4);
    return params;
  },
} as const;
