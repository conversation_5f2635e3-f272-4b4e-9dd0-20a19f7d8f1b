import React, {createContext, useState, useContext, ReactNode} from 'react';

interface ShowAyahListContextType {
  markDownTitle: string;
  setMarkDownTitle: (value: string) => void;
}

const ShowAyahListContext = createContext<ShowAyahListContextType | undefined>(
  undefined,
);

export const useShowAyahListContext = (): ShowAyahListContextType => {
  const context = useContext(ShowAyahListContext);
  if (!context) {
    throw new Error(
      'useShowAyahListContext must be used within a ShowAyahListProvider',
    );
  }
  return context;
};

interface AyahListProviderProps {
  children: ReactNode;
}
export const ShowAyahListProvider: React.FC<AyahListProviderProps> = ({
  children,
}) => {
  const [markDownTitle, setMarkDownTitle] = useState<string>('');

  return (
    <ShowAyahListContext.Provider
      value={{
        markDownTitle,
        setMarkDownTitle,
      }}>
      {children}
    </ShowAyahListContext.Provider>
  );
};
