import * as React from 'react';
import {
  createNavigationContainerRef,
  NavigationContainer,
} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Feather from 'react-native-vector-icons/Feather';
import QiblaCompass from '../screens/QiblaCompass';
import DrawerTab from './DrawerTab';
import PrayerSettings from '../screens/PrayerSettings';
import type {TMainStackProps} from '../types/types';
import colors from '../constants/colors';
import {StyleSheet} from 'react-native';
import {hp} from '../constants/constants';
import QiblaFaq from '../screens/QiblaFaq';

export const navigationRef = createNavigationContainerRef<TMainStackProps>();
const Stack = createNativeStackNavigator<TMainStackProps>();
const RootNavigation = () => {

  const headerLeftBack = () => (
    <Feather
      name='chevron-left'
      size={hp(3.5)}
      color={colors.primaryWhite}
      onPress={() => navigationRef.goBack()}
      style={styles.headerContainer}
    />
  );

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator initialRouteName='DrawerTab'>
        <Stack.Screen
          name='DrawerTab'
          component={DrawerTab}
          options={{
            headerShown: false,
            animation: 'slide_from_right',
          }}
        />
        <Stack.Screen
          name='qiblaCompass'
          component={QiblaCompass}
          options={{
            headerShown: false,
            animation: 'slide_from_right',
          }}
        />
        <Stack.Screen
          name='prayerSettings'
          component={PrayerSettings}
          options={{
            headerShown: true,
            animation: 'slide_from_right',
            headerLeft: () => headerLeftBack(),
            headerTitle: 'Prayer Settings',
            headerTitleStyle: {
              color: 'white',
              fontSize: 16,
              fontWeight: '500',
            },
            headerStyle: {backgroundColor: colors.alimLightBlue},
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name='qiblaFaq'
          component={QiblaFaq}
          options={{
            headerShown: false,
            animation: 'slide_from_right',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default RootNavigation;
const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
  },
  image: {
    width: 18,
    height: 18,
  },
  imageContainer: {
    paddingRight: 10,
    justifyContent: 'center',
  },
});

