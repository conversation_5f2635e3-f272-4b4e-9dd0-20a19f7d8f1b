import {
  Image,
  ImageSourcePropType,
  Linking,
  Share,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Platform,
} from "react-native";
import React from "react";
import { createDrawerNavigator } from "@react-navigation/drawer";
import { StackNavigationProp } from "@react-navigation/stack";
import { TMainStackProps } from "../types/types";
import QiblaCompass from "../screens/QiblaCompass";
import {
  donateUsUrl,
  hp,
  otherAppAndroid,
  otherAppIos,
  shareAppAndroid,
  shareAppIos,
} from "../constants/constants";
import colors from "../constants/colors";
import Prayer from "../screens/Prayer";
import { setAboutVisibility } from "../redux/slices/prayerSlice";
import { navigationRef } from "./RootNavigation";
import Feather from "react-native-vector-icons/Feather";
import { DrawerActions } from "@react-navigation/native";
import { useAppDispatch } from "../redux";

const DrawerArray = [
  {
    route: "prayer",
    label: "Prayer Time",
    component: Prayer,
    icon: require("../assets/images/prayer-time.png"),
  },
  {
    route: "qibla",
    label: "Qibla Finder",
    component: QiblaCompass,
    icon: require("../assets/images/qibla.png"),
  },
  {
    route: "qiblaCompassFaq",
    label: "Qibla FAQ",
    component: "",
    icon: require("../assets/images/drawerTab/faq.png"),
  },
  {
    route: "donate-url",
    label: "Donate Now",
    component: "",
    icon: require("../assets/images/donation.png"),
  },
  {
    route: "url-share",
    label: "Share App",
    component: "",
    icon: require("../assets/images/drawerTab/share-app.png"),
  },
  {
    route: "url-other",
    label: "Our Other App",
    component: "",
    icon: require("../assets/images/drawerTab/other-app.png"),
  },
  {
    route: "about-us",
    label: "About",
    component: "",
    icon: require("../assets/images/drawerTab/info.png"),
  },
];

interface DrawerItemProps {
  icon: ImageSourcePropType;
  label: string;
  onPress: () => void;
}

const DrawerItem: React.FC<DrawerItemProps> = ({ icon, label, onPress }) => {
  return (
    <View style={styles.drawerItemView}>
      <TouchableOpacity style={styles.drawerItem} onPress={onPress}>
        <Image
          source={icon}
          style={styles.icon}
          resizeMode="contain"
          tintColor={colors.primaryWhite}
        />
        <Text style={[styles.label, { color: colors.primaryWhite }]}>
          {label}
        </Text>
      </TouchableOpacity>
    </View>
  );
};
interface MenuProps {
  navigation: StackNavigationProp<TMainStackProps>;
}

const DrawerTab: React.FC<MenuProps> = () => {
  const Drawer = createDrawerNavigator();
  const dispatch = useAppDispatch();

  const shareAction = async () => {
    let message = '';
    message =
      Platform.OS === 'ios'
        ? shareAppIos
        : shareAppAndroid;
    const result = await Share.share({
      message: `Alim Prayer Minder\n ${message}`,
    });
    if (result.action === Share.sharedAction) {
      if (result.activityType) {
      } else {
      }
    } else if (result.action === Share.dismissedAction) {}
  };

  const DrawerContent = ({ navigation }: { navigation: any }) => (
    <ScrollView
      bounces={false}
      style={{ flex: 1, backgroundColor: colors.alimLightBlue }}
      contentContainerStyle={{ paddingBottom: 50, marginTop: Platform.OS === 'ios' ? 80 : 30 }}
    >
      {DrawerArray.map((item, index) => (
        <DrawerItem
          key={index.toString()}
          icon={item.icon}
          label={item.label}
          onPress={() => {
            navigation.closeDrawer();
            switch (item.route) {
              case "prayer":
                break;
              case "qibla":
                navigation.navigate("qiblaCompass");
                break;
              case "qiblaCompassFaq":
                navigation.navigate("qiblaFaq");
                break;
              case "donate-url":
                Linking.openURL(donateUsUrl);
                break;
              case "url-share":
                shareAction();
                break;
              case "url-other":
                const otherUrl =
                  Platform.OS === "ios" ? otherAppIos : otherAppAndroid;
                Linking.openURL(otherUrl);
                break;
              case "about-us":
                dispatch(setAboutVisibility(true));
                break;
              default:
                console.error(`Unknown route: ${item.route}`);
            }
          }}
        />
      ))}
    </ScrollView>
  );
  const headerLeft = () => (
    <Feather
      name="menu"
      size={hp(3.0)}
      color={colors.primaryWhite}
      onPress={() => navigationRef.dispatch(DrawerActions.openDrawer())}
      style={styles.headerContainer}
    />
  );

  const headerRight = () => (
    <View style={styles.headerContainer}>
      <TouchableOpacity
        style={styles.imageContainer}
        onPress={() => {
          navigationRef.navigate("prayerSettings");
        }}
      >
        <Image
          source={require("../assets/images/drawerTab/settings.png")}
          style={styles.image}
          resizeMode="contain"
          tintColor={colors.primaryWhite}
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <Drawer.Navigator
      screenOptions={{ headerShown: false }}
      drawerContent={(props) => <DrawerContent {...props} />}
    >
      <Drawer.Screen
        key={0}
        name={"Drawer"}
        component={Prayer}
        options={{
          headerShown: true,
          headerLeft: () => headerLeft(),
          headerRight: () => headerRight(),
          headerTitle: "Prayer Time",
          headerTitleStyle: {
            color: "white",
            fontSize: 16,
            fontWeight: "500",
          },
          headerStyle: { backgroundColor: colors.alimLightBlue },
          headerTitleAlign: "center",
          drawerItemStyle: {
            backgroundColor: colors.lightBlue,
          },
          drawerType: "front",
          drawerStyle: { backgroundColor: colors.lightBlue },
          drawerActiveBackgroundColor: colors.grey,
        }}
      />
    </Drawer.Navigator>
  );
};

export default DrawerTab;

const styles = StyleSheet.create({
  drawerItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  drawerItemView: {
    height: 50,
    paddingLeft: 24,
    justifyContent: "center",
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 16,
  },
  label: {
    fontSize: 16,
  },
  leftPaddings: { left: 15 },
  rightPaddings: { right: 15 },
  listItems: {
    flexDirection: "row",
  },
  headerContainer: {
    flexDirection: "row",
    paddingHorizontal: 24,
  },
  image: {
    width: 18,
    height: 18,
  },
  imageContainer: {
    justifyContent: "center",
  },
});
