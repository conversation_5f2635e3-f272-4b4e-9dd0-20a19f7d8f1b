import {
  DrawerActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
import type { TMainStackProps } from '../types/types';

export const navigationRef = createNavigationContainerRef<TMainStackProps>();

export const navigate = <T extends keyof TMainStackProps>(
  ...args: T extends string
    ? [screen: T, params?: TMainStackProps[T]]
    : [screen: T, params?: undefined]
): void => {
  if (navigationRef.isReady()) {
    const [screen, params] = args;
    if (params) {
      navigationRef.navigate(screen, params);
    } else {
      navigationRef.navigate(screen as never);
    }
  }
};

export const toggleDrawer = () => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(DrawerActions.openDrawer());
  }
};
export const closeDrawer = () => {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(DrawerActions.closeDrawer());
  }
};

export const goBack = () => {
  if (navigationRef.isReady()) {
    navigationRef.goBack();
  }
};
