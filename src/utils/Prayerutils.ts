import axios from 'axios';
import Config from 'react-native-config';
import { timeZoneGoogleUrl } from '../constants/constants';

export const getAutoCalculationMethod = (timezone: string) => {
  if (timezone.includes('Asia')) {
    return timezone.includes('Dubai') ||
      timezone.includes('Qatar') ||
      timezone.includes('Riyadh') ||
      timezone.includes('Aden') ||
      timezone.includes('Bahrain') ||
      timezone.includes('Muscat')
      ? 'Makkah'
      : 'Karachi';
  } else if (timezone.includes('America')) {
    return 'ISNA';
  } else if (timezone.includes('Africa')) {
    return 'Egypt';
  } else if (timezone.includes('Europe')) {
    return 'Tehran';
  }
  return 'Tehran';
};

export const getCalculationMethod = async (lat: string, long: string) => {
  try {
    const response = await axios.get(
      `${timeZoneGoogleUrl}${lat},${long}&timestamp=${Math.floor(
        Date.now() / 1000,
      )}&key=${Config.GOOGLE_MAPS_API_KEY}`,
    );
    const calcMethod = getAutoCalculationMethod(response.data.timeZoneId);
    return {calcMethod, timeZoneId: response.data.timeZoneId};
  } catch (error) {}
};
