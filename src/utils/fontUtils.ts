import {RFValue} from 'react-native-responsive-fontsize';

export const fontFamily = {
  bold: 'Inter-Bold',
  medium: 'Inter-Medium',
  regular: 'Inter-Regular',
};

export const fontSize = {
  font10: RFValue(10, 812),
  font11: RFValue(11, 812),
  font12: RFValue(12, 812),
  font13: RFValue(13, 812),
  font14: RFValue(14, 812),
  font15: RFValue(15, 812),
  font16: RFValue(16, 812),
  font17: RFValue(17, 812),
  font18: RFValue(18, 812),
  font19: RFValue(19, 812),
  font20: RFValue(20, 812),
  font21: RFValue(21, 812),
  font22: RFValue(22, 812),
  font23: RFValue(23, 812),
  font24: RFValue(24, 812),
  font25: RFValue(25, 812),
  font26: RFValue(26, 812),
  font27: RFValue(27, 812),
  font28: RFValue(28, 812),
  font29: RFValue(29, 812),
  font30: RFValue(30, 812),
  font31: RFValue(31, 812),
  font32: RFValue(32, 812),
  font33: RFValue(33, 812),
  font34: RFValue(34, 812),
  font35: RFValue(35, 812),
  font38: RFValue(38, 812),
  font48: RFValue(48, 812),
  font52: RFValue(53, 812),
  font70: RFValue(70, 812),
  font75: RFValue(75, 812),
};
