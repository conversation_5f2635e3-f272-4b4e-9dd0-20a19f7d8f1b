import {Coordinates, PrayerTimes} from 'adhan';
import BackgroundService from 'react-native-bg-actions';
import {prayerData} from '../data/prayerData';
import notifService from './notification';
import store from '../redux/store/store';

export default class PrayerService {
  static capitalizeName(value: string) {
    switch (value) {
      case 'fajr':
        return 'Fajr';
      case 'dhuhr':
        return 'Zuhr';
      case 'asr':
        return 'Asr';
      case 'maghrib':
        return 'Maghrib';
      case 'isha':
        return 'Isha';
      case 'sunrise':
        return 'Sunrise';
    }
  }

  async getCurrentPrayer(prayerSettings: any) {
    const params = prayerData.getCalculationParams(prayerSettings.Method);
    params.madhab = prayerSettings.Juristics === 'Hanafi' ? 'hanafi' : 'shafi';
    const coordinates = new Coordinates(
      Number(prayerSettings.lat),
      Number(prayerSettings.lng),
    );
    const date: Date = new Date();
    const prayerTimes = new PrayerTimes(coordinates, date, params);
    const prayer: any = {};
    prayer.name = PrayerService.capitalizeName(prayerTimes.currentPrayer());
    prayer.time = PrayerService.getAdjustedTime(
      prayerTimes,
      prayerSettings.adjustments,
      prayer.name,
    );
    return prayer;
  }

  getNextPrayer(prayerSettings: any): {
    name: string;
    time: string;
    remaining: number;
    currentPrayer: string;
    settings: any;
  } {
    const params = prayerData.getCalculationParams(prayerSettings.Method);
    params.madhab = prayerSettings.Juristics === 'Hanafi' ? 'hanafi' : 'shafi';

    const coordinates = new Coordinates(
      Number(prayerSettings.lat),
      Number(prayerSettings.lng),
    );
    const date: Date = new Date();
    let prayerTimes = new PrayerTimes(coordinates, date, params);
    const prayer: any = {};

    const nextPrayer = prayerTimes.nextPrayer();
    const currentPrayer = prayerTimes.currentPrayer();

    if (nextPrayer == 'none' && currentPrayer == 'isha') {
      // If last prayer of today, set reminder for tomorrow's Fajr
      date.setDate(date.getDate() + 1); // Gets tomorrow's date
      prayerTimes = new PrayerTimes(coordinates, date, params);
      prayer.name = 'Fajr';
    } else {
      prayer.name = PrayerService.capitalizeName(prayerTimes.nextPrayer());
    }

    prayer.time = PrayerService.getAdjustedTime(
      prayerTimes,
      prayerSettings.adjustments,
      prayer.name,
    );

    // After midnight -current prayer =='none'
    prayer.currentPrayer =
      currentPrayer == 'none'
        ? 'Tahajjud'
        : PrayerService.capitalizeName(currentPrayer);
    prayer.remaining = prayer.time.getTime() - new Date().getTime(); // duration to prayer time in ms
    // prayer.settings = prayerSettings;
    return prayer;
  }

  getPrayerTime(
    prayerSettings: any,
    adjustments: any,
    prayerName: string,
    date: any,
  ) {
    const params = prayerData.getCalculationParams(prayerSettings.Method);

    const juristicsValue = prayerSettings.Juristics;
    params.madhab = juristicsValue === 'Hanafi' ? 'hanafi' : 'shafi';
    const coordinates = new Coordinates(
      Number(prayerSettings.lat),
      Number(prayerSettings.lng),
    );
    const selDate: Date = date.toDate();
    const prayerTimes = new PrayerTimes(coordinates, selDate, params);
    const adjustedTime = PrayerService.getAdjustedTime(
      prayerTimes,
      adjustments,
      prayerName,
    );

    return PrayerService.getFormattedTime(adjustedTime);
  }

  static getFormattedTime(dateTimeString: any) {
    const dateTime = new Date(dateTimeString);
    const timezone =
      store.getState().prayer.prayerSettings.calculation?.Timezone;
    const formattedTime12 = new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
      timeZone: timezone,
    }).format(dateTime);
    return formattedTime12;
  }

  static getFormattedZoneTime(dateTimeString: any) {
    const dateTime = new Date(dateTimeString);
    const formattedTime12 = new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    }).format(dateTime);
    return formattedTime12;
  }

  static getFormattedDuration(milliSec: number) {
    const date = new Date(milliSec);
    let hh: any = date.getUTCHours();
    let mm: any = date.getUTCMinutes();
    hh = hh === 0 ? '' : `${hh}h `;
    mm = hh === 0 && mm === 0 ? '' : `${mm}m `;
    const t = hh + mm;
    return t;
  }

  // Adds adjusted time to calculated time
  static getAdjustedTime(
    prayerTimes: PrayerTimes,
    adjustments: any,
    prayerName: string,
  ) {
    let prayerTime: Date | undefined;
    let adjustment = 0;

    if (
      adjustments.Enabled &&
      prayerName !== 'Sunrise' &&
      prayerName !== 'Sunset'
    ) {
      adjustment = adjustments[prayerName];
    }

    switch (prayerName) {
      case 'Fajr': {
        prayerTime = prayerTimes.fajr;

        break;
      }
      case 'Zuhr': {
        prayerTime = prayerTimes.dhuhr;

        break;
      }
      case 'Asr': {
        prayerTime = prayerTimes.asr;

        break;
      }
      case 'Maghrib': {
        prayerTime = prayerTimes.maghrib;

        break;
      }
      case 'Isha': {
        prayerTime = prayerTimes.isha;

        break;
      }
      case 'Sunrise': {
        prayerTime = prayerTimes.sunrise;

        break;
      }
      case 'Sunset': {
        prayerTime = prayerTimes.sunset;

        break;
      }
      default:
      // Do nothing
    }

    const adjustedTime = prayerTime
      ? new Date(prayerTime.getTime() + adjustment * 60000)
      : null;
    return adjustedTime;
  }

  // Schedule prayer alert for iOS
  schedulePrayerAlert(
    prayerSettings: any,
    prayerName: string,
    date: Date,
    isLast: boolean,
  ) {
    const calculationParams = prayerData.getCalculationParams(
      prayerSettings.Method,
    );
    const coordinates = new Coordinates(
      Number(prayerSettings.lat),
      Number(prayerSettings.lng),
    );

    const prayerTimes = new PrayerTimes(coordinates, date, calculationParams);
    const adjustedTime = PrayerService.getAdjustedTime(
      prayerTimes,
      prayerSettings.adjustments,
      prayerName,
    );

    const currentTime = new Date();
    if (adjustedTime && adjustedTime > currentTime) {
      const prayerEnabled = prayerSettings[`${prayerName}Reminder`] ?? false;
      // const alertType = prayerSettings[`${prayerName}Audio`] ?? 'silent';
      const alertType = 'azan';
      if (prayerEnabled) {
        const params = {
          alertType,
          time: adjustedTime,
          title: `Adhan Time: ${prayerName}`,
          message: isLast
            ? 'Please open the app again as soon as possible to get more reminders'
            : `This is time for ${prayerName} prayer.`,
        };
        notifService.scheduleNotifiOS(params);
      }
    }
  }

  // Preschedule prayer times for 12 days in iOS
  iosScheduleAction(settings: any) {
    notifService.removeAllIOSRequest();
    const sheduledPrayerTimes: [string, Date][] = [];
    for (let day = 0; day < 12; day++) {
      const newDate = new Date();
      newDate.setDate(newDate.getDate() + day);
      for (let i = 0; i < 5; i++) {
        const prayerName = prayerData.prayerList[i];
        if (settings[`${prayerName}Reminder`]) {
          sheduledPrayerTimes.push([prayerName, newDate]);
        }
      }
    }
    sheduledPrayerTimes.forEach((data, index) => {
      const isLast = index == sheduledPrayerTimes.length - 1;
      new PrayerService().schedulePrayerAlert(
        settings,
        data[0],
        data[1],
        isLast,
      );
    });
  }

  // The task to be repeated when the background schedule is active - for android
  async backgroundTask(id: number, taskDataArguments: any) {
    const {interval, prayer} = taskDataArguments;
    let prayerName = prayer.name;
    let prayerTime = prayer.time;
    const prayerSettings = prayer.settings;
    let remainingTime = prayer.remaining;
    let countDownTime = 0; // seconds
    await new Promise(async () => {
      for (let i = 0; BackgroundService.isRunning(); i++) {
        // Task to run in each minute
        if (i % 60 == 0 && countDownTime == 0) {
          // Skip previously scheduled tasks (if id doesn't match)
          if (id !== bgTaskIndex) {
            return;
          }

          // Check for next prayer in every min
          const nextPrayer = new PrayerService().getNextPrayer(prayerSettings);
          prayerName = nextPrayer.name;
          prayerTime = nextPrayer.time;
          remainingTime = nextPrayer.remaining;

          // Starts countdown if next prayer in less than 2 min
          if (remainingTime < 120000) {
            countDownTime = Math.floor(remainingTime / 1000);
          }

          // Updates the time & duration in notification bar
          const timeFormatted = PrayerService.getFormattedTime(prayerTime);
          const durationFormatted =
            PrayerService.getFormattedDuration(remainingTime);

          const title =
            nextPrayer.currentPrayer == 'Sunrise'
              ? `Next Prayer: Zuhr after ${durationFormatted}`
              : `Current Prayer: ${nextPrayer.currentPrayer} ends in ${durationFormatted}`;

          const description =
            prayerName == 'Sunrise'
              ? `Sunrise Time : ${timeFormatted} - Alim.org`
              : `Next Prayer: ${nextPrayer.name} at ${timeFormatted}`;

          BackgroundService.updateNotification({
            taskTitle: title,
            taskDesc: description,
          });
        }

        // If count down enabled, updates remaining sec in notification bar
        if (countDownTime > 0) {
          countDownTime--;

          const timeFormatted = PrayerService.getFormattedTime(prayerTime);
          BackgroundService.updateNotification({
            taskTitle:
              prayerName == 'Sunrise'
                ? `Sunrise in ${countDownTime} seconds`
                : `Next prayer: ${prayerName} in ${countDownTime} seconds`,
            taskDesc:
              prayerName == 'Sunrise'
                ? `Sunrise Time : ${timeFormatted} - Alim.org`
                : `Prayer Time : ${timeFormatted} - Alim.org`,
          });

          // Showing alert when reached prayer time
          if (countDownTime == 0) {
            const prayerEnabled =
              prayerSettings[`${prayerName}Reminder`] ?? false;
            const alertType = prayerSettings[`${prayerName}Audio`] ?? 'silent';
            if (prayerEnabled) {
              const params = {
                alertType,
                title: `Adhan Time: ${prayerName}`,
                message: `This is time for ${prayerName} prayer.`,
                color: prayerSettings.color,
                screen: 'prayer',
              };
              notifService.scheduleNotif(params);
            }
          }
        }
        await sleep(interval); // Sleeps for interval set in schedule params.
      }
    });
  }

  // Initiates background task for android
  async scheduleReminder(prayerSettings: any, callback?: any) {
    const nextPrayer = this.getNextPrayer(prayerSettings);
    nextPrayer.settings = prayerSettings;

    const initiateTask = async () => {
      bgTaskIndex++; // Increment background task index
      const timeFormatted = PrayerService.getFormattedTime(nextPrayer.time);
      const durationFormatted = PrayerService.getFormattedDuration(
        nextPrayer.remaining,
      );
      const options = {
        taskName: 'Alim',
        taskTitle: `Current Prayer: ${nextPrayer.currentPrayer}`,
        taskDesc: `Next:${nextPrayer.name} at ${timeFormatted} (${durationFormatted})`,
        taskIcon: {
          name: 'ic_notification',
          type: 'drawable',
        },
        color: prayerSettings.color ?? '#305675',
        linkingURI: 'alimprayer://prayer', // deep link url
        parameters: {
          interval: 1000, // (ms) inteval to fire background callback
          prayer: nextPrayer,
        },
      };

      try {
        await BackgroundService.start(
          this.backgroundTask.bind(null, bgTaskIndex),
          options,
        );
        if (callback) {
          callback(true);
        }
      } catch (e) {
        console.error('Error', e);
        if (callback) {
          callback(false);
        }
      }
    };

    if (BackgroundService.isRunning()) {
      await this.stopScheduler(() => initiateTask());
    } else {
      initiateTask();
    }
  }

  async stopScheduler(callback?: any) {
    if (BackgroundService.isRunning()) {
      try {
        await BackgroundService.stop();
        BackgroundService.removeAllListeners();
        if (callback) {
          callback();
        }
      } catch (e) {
        console.error('Error', e);
      }
    }
  }
}

const sleep = (time: any) =>
  new Promise<void>(resolve => {
    setTimeout(() => resolve(), time);
  });

let bgTaskIndex = 0;
