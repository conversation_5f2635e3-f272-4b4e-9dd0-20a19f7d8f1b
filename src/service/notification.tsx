import PushNotification, {Importance} from 'react-native-push-notification';
import {check, PERMISSIONS, request, RESULTS} from 'react-native-permissions';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import {Platform} from 'react-native';
import notifee, {AndroidImportance, AuthorizationStatus} from '@notifee/react-native';

class NotifService {
  lastChannelCounter: any;

  lastId: any;

  constructor() {
    this.lastId = 0;
    this.lastChannelCounter = 0;

    this.createDefaultChannels();

    // NotificationHandler.attachRegister(onRegister);
    // NotificationHandler.attachNotification(onNotification);

    // Clear badge number at start
    PushNotification.getApplicationIconBadgeNumber(function (number) {
      if (number > 0) {
        PushNotification.setApplicationIconBadgeNumber(0);
      }
    });

    PushNotification.getChannels(function () {});

    PushNotification.configure({
      // (optional) Called when Token is generated (iOS and Android)
      onRegister() {},

      // (required) Called when a remote is received or opened, or local notification is opened
      // On opened notification- navigates to the screen detected
      onNotification(notification) {
        // if (notification) navigate('drawer', { screen: 'prayer' });
        notification.finish(PushNotificationIOS.FetchResult.NoData);
      },

      // (optional) Called when Registered Action is pressed and invokeApp is false, if true onNotification will be called (Android)
      onAction() {},

      // (optional) Called when the user fails to register for remote notifications. Typically occurs when APNS is having issues, or the device is a simulator. (iOS)
      onRegistrationError(err) {
        console.error(err.message, err);
      },

      // IOS ONLY (optional): default: all - Permissions to register.
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },

      // Should the initial notification be popped automatically
      // default: true
      popInitialNotification: true,

      /**
       * (optional) default: true
       * - Specified if permissions (ios) and token (android and ios) will requested or not,
       * - if not, you must call PushNotificationsHandler.requestPermissions() later
       * - if you are not using remote notification or do not have Firebase installed, use this:
       *     requestPermissions: Platform.OS === 'ios'
       */
      requestPermissions: Platform.OS === 'ios',
    });
  }

  createDefaultChannels() {
    PushNotification.createChannel(
      {
        channelId: 'alim-azan', // (required)
        channelName: 'Alim-Azan', // (required)
        channelDescription: 'Prayer alert - Azaan', // (optional) default: undefined.
        playSound: true,
        soundName: 'azan', // (optional) See `soundName` parameter of `localNotification` function
        importance: Importance.HIGH, // (optional) default: Importance.HIGH. Int value of the Android notification importance
        // vibrate: false, // (optional) default: true. Creates the default vibration pattern if true.
      },
      created =>
        console.warn(`createChannel 'alim-default' returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );
  }

  createOrUpdateChannel() {
    // this.lastChannelCounter++;
    PushNotification.createChannel(
      {
        channelId: 'custom-channel-id', // (required)
        channelName: `Custom channel - Counter: ${this.lastChannelCounter}`, // (required)
        channelDescription: `A custom channel to categorise your custom notifications. Updated at: ${Date.now()}`, // (optional) default: undefined.
        soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
        importance: Importance.HIGH, // (optional) default: Importance.HIGH. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration pattern if true.
      },
      created => console.warn(`createChannel returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );
  }

  popInitialNotification() {
    PushNotification.popInitialNotification(() => {});
  }

  localNotif(params: any) {
    // this.lastId++; // If notification don't need to update, change the id
    const channel = `alim-${params.alertType}`;
    PushNotification.localNotification({
      /* Android Only Properties */
      channelId: channel,
      ticker: 'Alim Notification Ticker', // (optional)
      autoCancel: true, // (optional) default: true
      largeIcon: 'ic_launcher', // (optional) default: "ic_launcher"
      smallIcon: 'ic_notification', // (optional) default: "ic_notification" with fallback for "ic_launcher"
      // bigText: 'My big text that will be shown when notification is expanded', // (optional) default: "message" prop
      // subText: 'Never miss salah', // (optional) default: none
      color: params.color, // (optional) default: system default
      vibrate: true, // (params.alerType == 'vibrate'), // (optional) default: true
      vibration: 300, // vibration length in milliseconds, ignored if vibrate=false, default: 1000
      // tag: 'some_tag', // (optional) add tag to message
      group: 'prayer', // (optional) add group to message
      groupSummary: false, // (optional) set this notification to be the group summary for a group of notifications, default: false
      ongoing: false, // (optional) set whether this is an "ongoing" notification
      // actions: ['Yes', 'No'], // (Android only) See the doc for notification actions to know more
      invokeApp: true, // (optional) This enable click on actions to bring back the application to foreground or stay in background, default: true

      when: null, // (optionnal) Add a timestamp pertaining to the notification (usually the time the event occurred). For apps targeting Build.VERSION_CODES.N and above, this time is not shown anymore by default and must be opted into by using `showWhen`, default: null.
      usesChronometer: false, // (optional) Show the `when` field as a stopwatch. Instead of presenting `when` as a timestamp, the notification will show an automatically updating display of the minutes and seconds since when. Useful when showing an elapsed time (like an ongoing phone call), default: false.
      timeoutAfter: null, // (optional) Specifies a duration in milliseconds after which this notification should be canceled, if it is not already canceled, default: null

      /* iOS only properties */
      // category: 'reminder', // (optional) default: empty string
      // subtitle: 'Never miss salah', // (optional) smaller title below notification title

      /* iOS and Android properties */
      id: this.lastId, // (optional) Valid unique 32 bit integer specified as string. default: Autogenerated Unique ID
      title: params.title, // (optional)
      message: params.message, // (required)
      userInfo: {screen: params.screen}, // (optional) default: {} (using null throws a JSON value '<null>' error)
      playSound: false, //! !soundName, // (optional) default: true
      // soundName: 'remindermusic.mp3', //default | azanmakkah | remindermusic // (optional) Sound to play when the notification is shown. Value of 'default' plays the default sound. It can be set to a custom sound such as 'android.resource://com.xyz/raw/my_sound'. It will look for the 'my_sound' audio file in 'res/raw' directory and play it. default: 'default' (default sound is played)
      number: 10, // (optional) Valid 32 bit integer specified as string. default: none (Cannot be zero)
    });
  }

  async scheduleNotif(params: {
    alertType: string;
    color: string;
    message: string;
    screen: string;
    title: string;
  }) {
    const channelId = await notifee.createChannel({
      id: 'alim-azan',
      name: 'Default Channel',
      sound: 'azan',
      importance: AndroidImportance.HIGH,
    });
    await notifee.displayNotification({
      title: params.title,
      body: params.message,
      android: {
        channelId,
        smallIcon: 'ic_notification',
        importance: AndroidImportance.HIGH, // optional, defaults to 'ic_launcher'.
        // pressAction is needed if you want the notification to open the app when pressed
        pressAction: {
          id: 'default',
        },
      },
      ios: {
        sound: 'azan',
        critical: true,
      },
      data: {
        screen: 'prayer',
      },
    });
  }

  scheduleNotifiOS(params: any) {
    PushNotificationIOS.scheduleLocalNotification({
      fireDate: params.time.toISOString(),
      // fireDate: new Date(Date.now() - 30 * 1000),
      // id: this.lastId,
      alertTitle: params.title,
      alertBody: params.message,
      soundName: 'azan.caf', // The sound played when the notification is fired (optional).
      // badge: 1,
      // userInfo: { screen: 'prayer' },
      // isSilent: params.alertType == 'silent' || params.alertType == 'vibrate',
    });
  }

  async requestPermission() {
    const settings = await notifee.requestPermission();
    return settings.authorizationStatus >= AuthorizationStatus.AUTHORIZED;
  }

  async checkPermission() {
    const settings = await notifee.getNotificationSettings();
    return settings.authorizationStatus >= AuthorizationStatus.AUTHORIZED;
  }

  cancelNotif(id?: any) {
    PushNotification.cancelLocalNotification(id ?? this.lastId);
  }

  cancelAll() {
    PushNotification.cancelAllLocalNotifications();
  }

  removeAllIOSRequest() {
    PushNotificationIOS.removeAllPendingNotificationRequests();
  }

  abandonPermissions() {
    PushNotification.abandonPermissions();
  }

  getScheduledLocalNotifications(callback: any) {
    PushNotification.getScheduledLocalNotifications(callback);
  }

  getDeliveredNotifications(callback: any) {
    PushNotification.getDeliveredNotifications(callback);
  }
}

const notifService = new NotifService();
export default notifService;
