import AsyncStorage from '@react-native-async-storage/async-storage';
import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import {RootState} from '../store/store';

interface LocationHistoryItem {
  location: string;
  lat: string;
  lng: string;
  Method: string;
  Timezone: string;
}

interface CalculationSettings {
  location: string;
  lat: string;
  lng: string;
  Method: string;
  Juristics: string;
  Highlats: string;
  Timezone: string;
}

interface Adjustments {
  Enabled: boolean;
  Fajr: number;
  Zuhr: number;
  Asr: number;
  Maghrib: number;
  Isha: number;
}

interface NotificationSettings {
  FajrReminder: boolean;
  ZuhrReminder: boolean;
  AsrReminder: boolean;
  MaghribReminder: boolean;
  IshaReminder: boolean;
  SunriseReminder: boolean;
}

interface PrayerSettings {
  calculation: CalculationSettings;
  searchLocationHistory: LocationHistoryItem[];
  adjustments: Adjustments;
  notifAdjustments: Adjustments;
  notification: NotificationSettings;
}

interface State {
  currentTooltip: number;
  prayerSettings: PrayerSettings;
  isAboutVisible: boolean;
}

const initialState: State = {
  currentTooltip: 0,
  prayerSettings: {
    calculation: {
      location: 'Maryland, NY 12116, USA (Default)',
      lat: '42.5364661',
      lng: '-74.8865453',
      Method: 'ISNA',
      Juristics: 'Shafii (Standard)',
      Highlats: 'Angle /60th of Night',
      Timezone: 'America/New_York',
    },
    searchLocationHistory: [],
    adjustments: {
      Enabled: false,
      Fajr: 0,
      Zuhr: 0,
      Asr: 0,
      Maghrib: 0,
      Isha: 0,
    },
    notifAdjustments: {
      Enabled: false,
      Fajr: 0,
      Zuhr: 0,
      Asr: 0,
      Maghrib: 0,
      Isha: 0,
    },
    notification: {
      FajrReminder: false,
      ZuhrReminder: false,
      AsrReminder: false,
      MaghribReminder: false,
      IshaReminder: false,
      SunriseReminder: false,
    },
  },
  isAboutVisible: false,
};

type TNewData = Pick<
  (typeof initialState)['prayerSettings'],
  'adjustments' | 'calculation'
>;
type TPrayerCalculation =
  (typeof initialState)['prayerSettings']['calculation'];

type TPrayerAdjustments =
  (typeof initialState)['prayerSettings']['adjustments'];

type TPrayerNotfn = (typeof initialState)['prayerSettings']['notification'];

interface ValidationErrors {
  errorMessage: string;
  field_errors: Record<string, string>;
}

export const storeCalculationSettings = createAsyncThunk<
  TPrayerCalculation,
  Partial<TPrayerCalculation>,
  {
    state: RootState;
  }
>(
  'prayer/storeCalculationSettings',
  async (updateCalcData, {getState, rejectWithValue}) => {
    const {calculation: calculationSettings} = getState().prayer.prayerSettings;
    const updateData = {...calculationSettings, ...updateCalcData};
    try {
      await AsyncStorage.setItem(
        'CalculationSettings',
        JSON.stringify(updateData),
      );
      return updateData;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export const loadCalculationSettings = createAsyncThunk<
  TPrayerCalculation,
  string | undefined,
  {
    rejectValue: ValidationErrors;
    state: RootState;
  }
>(
  'prayer/loadCalculationSettings',
  async (storageKey, {rejectWithValue, getState}) => {
    const {calculation: calculationSettings} = getState().prayer.prayerSettings;
    try {
      const updateData = await AsyncStorage.getItem(
        storageKey ?? 'CalculationSettings',
      );
      if (updateData) {
        return JSON.parse(updateData);
      }
      await AsyncStorage.setItem(
        'CalculationSettings',
        JSON.stringify(calculationSettings),
      );
      return calculationSettings;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export const resetPrayerSettings = createAsyncThunk<
  TNewData,
  string | undefined,
  {
    rejectValue: ValidationErrors;
    state: RootState;
  }
>(
  'prayer/resetPrayerSettings',
  async (_storageKey, {rejectWithValue, getState}) => {
    const {prayerSettings} = getState().prayer;
    const {calculation} = prayerSettings;
    try {
      const adjData = initialState.prayerSettings.adjustments;

      const calcData = {
        ...initialState.prayerSettings.calculation,
        Method: calculation.Method,
        location: calculation.location,
        lat: calculation.lat,
        lng: calculation.lng,
        Timezone: calculation.Timezone,
      };

      await AsyncStorage.setItem(
        'CalculationSettings',
        JSON.stringify(calcData),
      );
      await AsyncStorage.setItem('Adjustments', JSON.stringify(adjData));
      loadAdjustments();

      const newData: TNewData = {
        calculation: initialState.prayerSettings.calculation,
        adjustments: initialState.prayerSettings.adjustments,
      };
      newData.calculation = calcData;
      newData.adjustments = adjData;

      return newData;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export const storeAdjustments = createAsyncThunk<
  TPrayerAdjustments,
  Partial<TPrayerAdjustments>,
  {
    state: RootState;
  }
>(
  'prayer/storeAdjustments',
  async (updateAdjData, {getState, rejectWithValue}) => {
    const {adjustments: prayerAdjustments} = getState().prayer.prayerSettings;
    const updateData = {...prayerAdjustments, ...updateAdjData};
    try {
      await AsyncStorage.setItem('Adjustments', JSON.stringify(updateData));
      return updateData;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export const loadAdjustments = createAsyncThunk<
  TPrayerAdjustments,
  string | undefined,
  {
    rejectValue: ValidationErrors;
    state: RootState;
  }
>('prayer/loadAdjustments', async (storageKey, {rejectWithValue, getState}) => {
  try {
    const updateData = await AsyncStorage.getItem(storageKey ?? 'Adjustments');
    if (updateData) {
      return JSON.parse(updateData);
    }
    // If no data found in AsyncStorage, saves with default values
    const {adjustments: prayerAdjustments} = getState().prayer.prayerSettings;
    await AsyncStorage.setItem(
      'Adjustments',
      JSON.stringify(prayerAdjustments),
    );
    return prayerAdjustments;
  } catch (err) {
    return rejectWithValue(err as ValidationErrors);
  }
});

export const storeNotfnSettings = createAsyncThunk<
  TPrayerNotfn,
  Partial<TPrayerNotfn>,
  {
    state: RootState;
  }
>(
  'prayer/storeNotfnSettings',
  async (updateNotfnData, {getState, rejectWithValue}) => {
    const {notification: notificationSettings} =
      getState().prayer.prayerSettings;
    const updateData = {...notificationSettings, ...updateNotfnData};
    try {
      await AsyncStorage.setItem('NotfnSettings', JSON.stringify(updateData));
      return updateData;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export const loadNotifnSettings = createAsyncThunk<
  TPrayerNotfn,
  string | undefined,
  {
    rejectValue: ValidationErrors;
    state: RootState;
  }
>(
  'prayer/loadNotifnSettings',
  async (storageKey, {rejectWithValue, getState}) => {
    const {notification: notificationSettings} =
      getState().prayer.prayerSettings;
    try {
      const updateData = await AsyncStorage.getItem(
        storageKey ?? 'NotfnSettings',
      );
      if (updateData) {
        return JSON.parse(updateData);
      }
      await AsyncStorage.setItem(
        'NotfnSettings',
        JSON.stringify(notificationSettings),
      );
      return notificationSettings;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

const prayerSlice = createSlice({
  name: 'prayer',
  initialState,
  reducers: {
    prayerSettings: (state, action) => {
      state.prayerSettings.calculation = {
        ...state.prayerSettings.calculation,
        Method: action.payload.Method,
        Juristics: action.payload.Juristics,
        Highlats: action.payload.Highlats,
      };
    },
    resetData: state => {
      return {
        ...state,
        FajrIncrementedTime: 0,
        ZuhrIncrementedTime: 0,
        AsrIncrementedTime: 0,
        MaghribIncrementedTime: 0,
        IshaIncrementedTime: 0,
        Adjustments: 'false',
        Method: 'Karachi',
        Juristics: 'Shafii (Standard)',
        Highlats: 'Angle /60th of Night',
      };
    },
    saveLocation: (state, action) => {
      state.prayerSettings.calculation = {
        ...state.prayerSettings.calculation,
        location: action.payload.location,
        lat: action.payload.lat,
        lng: action.payload.lng,
      };
    },
    saveLocationHistory: (state, action) => {
      const {location, lat, lng, Method, Timezone} = action.payload;
      if (state.prayerSettings.searchLocationHistory.length === 0) {
        state.prayerSettings.searchLocationHistory.unshift({
          location,
          lat,
          lng,
          Method,
          Timezone,
        });
      } else {
        const existingLocationIndex =
          state.prayerSettings.searchLocationHistory?.findIndex(
            entry =>
              entry.location === location &&
              entry.lat === lat &&
              entry.lng === lng,
          );
        if (existingLocationIndex !== -1) {
          state.prayerSettings.searchLocationHistory.splice(
            existingLocationIndex,
            1,
          );
        }
        state.prayerSettings.searchLocationHistory.unshift({
          location,
          lat,
          lng,
          Method,
          Timezone,
        });
      }
      state.prayerSettings.searchLocationHistory =
        state.prayerSettings.searchLocationHistory.slice(0, 5);
    },
    setCurrentTooltip: (state, action) => {
      state.currentTooltip = action.payload;
    },
    addCurrentTooltip: state => {
      state.currentTooltip = ++state.currentTooltip;
    },
    saveAlertStatus: (state, action) => {
      state.prayerSettings.notification = {
        ...state.prayerSettings.notification,
        ...action.payload,
      };
    },
    setAboutVisibility: (state, action) => {
      return {
        ...state,
        isAboutVisible: action.payload,
      };
    },
  },
  extraReducers: builder => {
    builder
      .addCase(storeNotfnSettings.fulfilled, (state, action) => {
        state.prayerSettings.notification = action.payload;
      })
      .addCase(storeCalculationSettings.fulfilled, (state, action) => {
        state.prayerSettings.calculation = action.payload;
      })
      .addCase(storeAdjustments.fulfilled, (state, action) => {
        state.prayerSettings.adjustments = action.payload;
      })
      .addCase(loadAdjustments.fulfilled, (state, action) => {
        state.prayerSettings.adjustments = action.payload;
      })
      .addCase(loadNotifnSettings.fulfilled, (state, action) => {
        state.prayerSettings.notification = action.payload;
      })
      .addCase(loadCalculationSettings.fulfilled, (state, action) => {
        state.prayerSettings.calculation = action.payload;
      })
      .addCase(resetPrayerSettings.fulfilled, (state, action) => {
        state.prayerSettings.calculation = action.payload.calculation;
        state.prayerSettings.adjustments = action.payload.adjustments;
      })
      .addCase(storeNotifAdjustments.fulfilled, (state, action) => {
        state.prayerSettings.notifAdjustments = action.payload;
      })
      .addCase(loadNotifAdjustments.fulfilled, (state, action) => {
        state.prayerSettings.notifAdjustments = action.payload;
      });
  },
});
export const storeNotifAdjustments = createAsyncThunk<
  TPrayerAdjustments,
  Partial<TPrayerAdjustments>,
  {
    state: RootState;
  }
>(
  'prayer/storeNotifAdjustments',
  async (updateAdjData, {getState, rejectWithValue}) => {
    const {notifAdjustments: prayerNotifAdjustments} =
      getState().prayer.prayerSettings;
    const updateData = {...prayerNotifAdjustments, ...updateAdjData};
    try {
      await AsyncStorage.setItem(
        'NotifAdjustments',
        JSON.stringify(updateData),
      );
      return updateData;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export const loadNotifAdjustments = createAsyncThunk<
  TPrayerAdjustments,
  string | undefined,
  {
    rejectValue: ValidationErrors;
    state: RootState;
  }
>(
  'prayer/loadNotifAdjustments',
  async (storageKey, {rejectWithValue, getState}) => {
    try {
      const updateData = await AsyncStorage.getItem(
        storageKey ?? 'NotifAdjustments',
      );
      if (updateData) {
        return JSON.parse(updateData);
      }
      // If no data found in AsyncStorage, saves with default values
      const {notifAdjustments: prayerNotifAdjustments} =
        getState().prayer.prayerSettings;
      await AsyncStorage.setItem(
        'NotifAdjustments',
        JSON.stringify(prayerNotifAdjustments),
      );
      return prayerNotifAdjustments;
    } catch (err) {
      return rejectWithValue(err as ValidationErrors);
    }
  },
);

export default prayerSlice.reducer;
export const {
  prayerSettings,
  saveLocation,
  resetData,
  saveAlertStatus,
  setAboutVisibility,
  setCurrentTooltip,
  addCurrentTooltip,
  saveLocationHistory,
} = prayerSlice.actions;
