import {StyleSheet, View} from 'react-native';
import React, {memo, useCallback, useEffect} from 'react';
import NetInfo from '@react-native-community/netinfo';
import {useDispatch} from 'react-redux';
import {setInternetStatus} from '../redux/slices/internetSlice';

interface IProps {
  children: React.ReactNode;
}

const InternetListener: React.FC<IProps> = ({children}): JSX.Element => {
  const dispatch = useDispatch();

  const listenIsInternetAvailable = useCallback(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const {isInternetReachable, isConnected} = state;
      const status = isInternetReachable && isConnected;
      dispatch(setInternetStatus(status));
    });
    return () => {
      unsubscribe();
    };
  }, [dispatch]);

  useEffect(() => {
    listenIsInternetAvailable();
  }, [listenIsInternetAvailable]);

  return <View style={styles.container}>{children}</View>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default memo(InternetListener);
