buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

allprojects {
    repositories {
        mavenCentral {
            content {
                excludeGroup "com.facebook.react"
            }
        }
    }
}

gradle.projectsEvaluated {
    allprojects {
        tasks.withType(Exec).configureEach { task ->
            if (task.commandLine && task.commandLine.size() > 0 && task.commandLine[0] == "node") {
                def nodeBinary = project.hasProperty("NODE_BINARY") ? project.getProperty("NODE_BINARY") : "/usr/local/bin/node"
                task.commandLine = [nodeBinary] + task.commandLine.drop(1)
            }
        }
    }
}

apply plugin: "com.facebook.react.rootproject"