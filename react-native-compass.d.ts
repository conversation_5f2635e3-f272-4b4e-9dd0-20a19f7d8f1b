// react-native-compass-heading.d.ts

declare module 'react-native-compass-heading' {
  export interface CompassHeading {
    heading: number;
    trueHeading: number;
    accuracy: number;
    timestamp: number;
  }

  export interface CompassHeadingError {
    code: number;
    message: string;
  }

  export interface CompassHeadingOptions {
    interval?: number;
  }

  export function start(
    interval?: number,
    callback?: (heading: CompassHeading) => void,
    errorCallback?: (error: CompassHeadingError) => void,
  ): void;

  export function stop(): void;
}
