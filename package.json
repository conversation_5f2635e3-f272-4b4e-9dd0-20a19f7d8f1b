{"name": "alimprayer", "version": "0.0.1", "private": true, "scripts": {"postinstall": "patch-package", "android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-navigation/drawer": "^7.5.3", "@react-navigation/elements": "^2.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "adhan": "^4.4.3", "axios": "^1.11.0", "moment": "^2.30.1", "moment-hijri": "^2.1.2", "qibla": "^1.1.0", "react": "19.0.0", "react-native": "0.79.0", "react-native-android-location-enabler": "^2.0.1", "react-native-bg-actions": "^1.0.17", "react-native-config": "^1.5.5", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.12.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-markdown-display": "^7.0.2", "react-native-modal": "^14.0.0-rc.1", "react-native-permissions": "^5.4.2", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.17.2", "react-native-responsive-fontsize": "^0.5.1", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-sensors": "^7.3.6", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.3.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@commitlint/cli": "^18.4.2", "@commitlint/config-conventional": "^18.4.2", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/eslint-config": "0.79.0", "@react-native/metro-config": "0.79.0", "@tsconfig/react-native": "^3.0.0", "@types/moment-hijri": "^2.1.4", "@types/react": "^19.0.0", "@types/react-native-push-notification": "^8.1.4", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.76.8", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}